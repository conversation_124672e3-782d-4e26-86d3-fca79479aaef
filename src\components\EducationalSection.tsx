import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  HeartIcon, 
  ShieldCheckIcon, 
  TrendingUpIcon, 
  UsersIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  InfoIcon,
  BookOpenIcon
} from 'lucide-react';

const EducationalSection: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const statistics = [
    {
      title: "Global Incidence",
      value: "417,367",
      subtitle: "New cases annually worldwide",
      icon: <UsersIcon className="w-6 h-6" />,
      color: "from-sky-500 to-blue-600",
      description: "Endometrial cancer is the 6th most common cancer in women globally"
    },
    {
      title: "5-Year Survival Rate",
      value: "95%",
      subtitle: "When detected early (Stage I)",
      icon: <HeartIcon className="w-6 h-6" />,
      color: "from-emerald-500 to-teal-600",
      description: "Early detection dramatically improves outcomes"
    },
    {
      title: "Age Factor",
      value: "60-70",
      subtitle: "Peak age range for diagnosis",
      icon: <TrendingUpIcon className="w-6 h-6" />,
      color: "from-purple-500 to-indigo-600",
      description: "Most cases occur after menopause"
    },
    {
      title: "Prevention Impact",
      value: "40%",
      subtitle: "Cases preventable through lifestyle",
      icon: <ShieldCheckIcon className="w-6 h-6" />,
      color: "from-orange-500 to-amber-600",
      description: "Healthy lifestyle significantly reduces risk"
    }
  ];

  const riskFactors = [
    { factor: "Age (>50 years)", risk: "High", percentage: 85 },
    { factor: "Obesity (BMI >30)", risk: "High", percentage: 75 },
    { factor: "Diabetes", risk: "Medium", percentage: 60 },
    { factor: "Family History", risk: "Medium", percentage: 55 },
    { factor: "PCOS", risk: "Medium", percentage: 50 },
    { factor: "Late Menopause", risk: "Low", percentage: 30 }
  ];

  const symptoms = [
    {
      symptom: "Abnormal Vaginal Bleeding",
      description: "Most common early symptom, especially post-menopause",
      urgency: "High",
      icon: <AlertTriangleIcon className="w-5 h-5" />
    },
    {
      symptom: "Pelvic Pain",
      description: "Persistent pain in the pelvic region",
      urgency: "Medium",
      icon: <InfoIcon className="w-5 h-5" />
    },
    {
      symptom: "Unusual Discharge",
      description: "Watery or blood-tinged discharge",
      urgency: "Medium",
      icon: <InfoIcon className="w-5 h-5" />
    },
    {
      symptom: "Painful Urination",
      description: "Difficulty or pain during urination",
      urgency: "Low",
      icon: <InfoIcon className="w-5 h-5" />
    }
  ];

  return (
    <section className="py-16 bg-gradient-to-br from-sky-50 via-blue-50 to-indigo-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div 
        className="absolute inset-0 opacity-5"
        style={{
          backgroundImage: `url('/medical-backgrounds/medical-pattern.svg')`,
          backgroundSize: '100px 100px',
          backgroundRepeat: 'repeat'
        }}
      />
      
      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl font-bold text-primary-navy mb-4">
            Understanding Endometrial Cancer
          </h2>
          <p className="text-lg text-medium-gray max-w-3xl mx-auto">
            Comprehensive information about endometrial cancer, including real-world statistics, 
            risk factors, and the importance of early detection.
          </p>
        </motion.div>

        {/* Statistics Cards */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {statistics.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-5`} />
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className={`p-3 rounded-lg bg-gradient-to-br ${stat.color} text-white`}>
                      {stat.icon}
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      2024 Data
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <h3 className="text-2xl font-bold text-primary-navy">
                      {stat.value}
                    </h3>
                    <p className="text-sm font-medium text-cerulean">
                      {stat.subtitle}
                    </p>
                    <p className="text-xs text-medium-gray">
                      {stat.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Educational Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="max-w-6xl mx-auto"
        >
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4 mb-8 bg-white/80 backdrop-blur-sm">
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <BookOpenIcon className="w-4 h-4" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="risk-factors">Risk Factors</TabsTrigger>
              <TabsTrigger value="symptoms">Symptoms</TabsTrigger>
              <TabsTrigger value="prevention">Prevention</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <Card className="border-0 shadow-lg bg-white/90 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-2xl text-primary-navy">
                    What is Endometrial Cancer?
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-medium-gray leading-relaxed">
                    Endometrial cancer is a type of cancer that begins in the uterus. It starts in the layer of cells 
                    that form the lining (endometrium) of the uterus. It's sometimes called uterine cancer, although 
                    there are other types of cancer that can form in the uterus.
                  </p>
                  <div className="grid md:grid-cols-2 gap-6 mt-6">
                    <div className="space-y-3">
                      <h4 className="font-semibold text-primary-navy">Key Facts:</h4>
                      <ul className="space-y-2 text-sm text-medium-gray">
                        <li className="flex items-start gap-2">
                          <CheckCircleIcon className="w-4 h-4 text-emerald-500 mt-0.5 flex-shrink-0" />
                          Most common gynecologic cancer in developed countries
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircleIcon className="w-4 h-4 text-emerald-500 mt-0.5 flex-shrink-0" />
                          Usually diagnosed at an early stage
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircleIcon className="w-4 h-4 text-emerald-500 mt-0.5 flex-shrink-0" />
                          High cure rate when detected early
                        </li>
                      </ul>
                    </div>
                    <div className="space-y-3">
                      <h4 className="font-semibold text-primary-navy">Global Impact:</h4>
                      <ul className="space-y-2 text-sm text-medium-gray">
                        <li>• 417,367 new cases annually worldwide</li>
                        <li>• 97,370 deaths per year globally</li>
                        <li>• Incidence increasing in many countries</li>
                        <li>• Higher rates in developed nations</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="risk-factors" className="space-y-6">
              <Card className="border-0 shadow-lg bg-white/90 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-2xl text-primary-navy">
                    Risk Factors Analysis
                  </CardTitle>
                  <CardDescription>
                    Understanding your risk factors can help with early detection and prevention
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {riskFactors.map((item, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="font-medium text-primary-navy">{item.factor}</span>
                          <Badge 
                            variant={item.risk === 'High' ? 'destructive' : item.risk === 'Medium' ? 'default' : 'secondary'}
                            className={
                              item.risk === 'High' ? 'bg-orange-100 text-orange-800' :
                              item.risk === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-green-100 text-green-800'
                            }
                          >
                            {item.risk} Risk
                          </Badge>
                        </div>
                        <Progress value={item.percentage} className="h-2" />
                        <p className="text-xs text-medium-gray">
                          {item.percentage}% correlation with increased risk
                        </p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="symptoms" className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                {symptoms.map((symptom, index) => (
                  <Card key={index} className="border-0 shadow-lg bg-white/90 backdrop-blur-sm">
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-lg ${
                          symptom.urgency === 'High' ? 'bg-orange-100 text-orange-600' :
                          symptom.urgency === 'Medium' ? 'bg-yellow-100 text-yellow-600' :
                          'bg-blue-100 text-blue-600'
                        }`}>
                          {symptom.icon}
                        </div>
                        <div>
                          <CardTitle className="text-lg text-primary-navy">
                            {symptom.symptom}
                          </CardTitle>
                          <Badge 
                            variant="outline" 
                            className={
                              symptom.urgency === 'High' ? 'border-orange-300 text-orange-700' :
                              symptom.urgency === 'Medium' ? 'border-yellow-300 text-yellow-700' :
                              'border-blue-300 text-blue-700'
                            }
                          >
                            {symptom.urgency} Priority
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-medium-gray">
                        {symptom.description}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="prevention" className="space-y-6">
              <Card className="border-0 shadow-lg bg-white/90 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-2xl text-primary-navy">
                    Prevention & Early Detection
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="font-semibold text-primary-navy">Lifestyle Modifications:</h4>
                      <ul className="space-y-2 text-sm text-medium-gray">
                        <li className="flex items-start gap-2">
                          <CheckCircleIcon className="w-4 h-4 text-emerald-500 mt-0.5 flex-shrink-0" />
                          Maintain healthy weight (BMI &lt; 25)
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircleIcon className="w-4 h-4 text-emerald-500 mt-0.5 flex-shrink-0" />
                          Regular physical activity (150 min/week)
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircleIcon className="w-4 h-4 text-emerald-500 mt-0.5 flex-shrink-0" />
                          Balanced diet rich in fruits and vegetables
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircleIcon className="w-4 h-4 text-emerald-500 mt-0.5 flex-shrink-0" />
                          Manage diabetes effectively
                        </li>
                      </ul>
                    </div>
                    <div className="space-y-4">
                      <h4 className="font-semibold text-primary-navy">Screening & Detection:</h4>
                      <ul className="space-y-2 text-sm text-medium-gray">
                        <li className="flex items-start gap-2">
                          <CheckCircleIcon className="w-4 h-4 text-emerald-500 mt-0.5 flex-shrink-0" />
                          Annual gynecologic exams
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircleIcon className="w-4 h-4 text-emerald-500 mt-0.5 flex-shrink-0" />
                          Report abnormal bleeding immediately
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircleIcon className="w-4 h-4 text-emerald-500 mt-0.5 flex-shrink-0" />
                          Consider genetic counseling if family history
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircleIcon className="w-4 h-4 text-emerald-500 mt-0.5 flex-shrink-0" />
                          Advanced screening for high-risk individuals
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </motion.div>
      </div>
    </section>
  );
};

export default EducationalSection;
