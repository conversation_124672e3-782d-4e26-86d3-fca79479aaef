import openslide
import numpy as np
import cv2
import os
import matplotlib.pyplot as plt
import time
from PIL import Image

# Define WSI file path - use a sample file that should be in the uploads folder
# Check if there are any files in the uploads folder
uploads_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
wsi_files = [f for f in os.listdir(uploads_dir) if os.path.isfile(os.path.join(uploads_dir, f))]

if wsi_files:
    wsi_path = os.path.join(uploads_dir, wsi_files[0])
    print(f"Using existing file: {wsi_path}")
else:
    # If no files found, use a default path
    wsi_path = r"C:\Users\<USER>\Downloads\WholeSlide.tif"
    print(f"No files found in uploads directory. Using default path: {wsi_path}")

# Output directory
output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'test_patches')
os.makedirs(output_dir, exist_ok=True)
print(f"Output directory created at: {output_dir}")

# === Reinhard Color Normalization ===
def reinhard_normalization(image, target_mean=None, target_std=None):
    """
    Applies Reinhard color normalization to standardize appearance.
    Uses LAB color space for normalization.
    """
    if target_mean is None:
        target_mean = [128, 128, 128]  # Default target means for LAB
    if target_std is None:
        target_std = [64, 64, 64]  # Default target stds for LAB
    
    # Convert to LAB color space
    lab_image = cv2.cvtColor(image, cv2.COLOR_RGB2LAB)
    lab_image = lab_image.astype(np.float32)
    
    # Calculate mean and std for each channel
    mean, std = cv2.meanStdDev(lab_image)
    mean = mean.flatten()
    std = std.flatten()
    
    # Normalize each channel
    for i in range(3):
        lab_image[:,:,i] = (lab_image[:,:,i] - mean[i]) * (target_std[i] / std[i]) + target_mean[i]
    
    # Clip values to valid range and convert back to RGB
    lab_image = np.clip(lab_image, 0, 255)
    normalized_image = cv2.cvtColor(lab_image.astype(np.uint8), cv2.COLOR_LAB2RGB)
    
    return normalized_image

# === Optimized Tissue Detection ===
def optimized_tissue_detection(image):
    """
    Efficient tissue detection using adaptive thresholding and morphological operations.
    Returns a binary mask where tissue regions are white.
    """
    # Convert to HSV and use saturation channel (better for tissue detection)
    hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
    saturation = hsv[:,:,1]
    
    # Adaptive thresholding
    mask = cv2.adaptiveThreshold(saturation, 255, cv2.ADAPTIVE_THRESH_MEAN_C, 
                                cv2.THRESH_BINARY, 51, 5)
    
    # Morphological operations to clean up the mask
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5,5))
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=1)
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=1)
    
    # Remove small artifacts
    mask = cv2.erode(mask, kernel, iterations=1)
    
    return mask

# === Non-overlapping Patch Extraction ===
def extract_non_overlapping_patches(slide, contours, thumbnail_size, patch_size, output_dir):
    """
    Extracts non-overlapping patches that cover tissue regions efficiently.
    Uses grid-based approach with tissue presence verification.
    """
    # Calculate scaling factors
    scale_x = slide.dimensions[0] / thumbnail_size[0]
    scale_y = slide.dimensions[1] / thumbnail_size[1]
    
    # Create a grid covering the entire WSI
    grid_x = int(slide.dimensions[0] / patch_size[0])
    grid_y = int(slide.dimensions[1] / patch_size[1])
    
    print(f"Grid dimensions: {grid_x}x{grid_y}")
    print(f"Scale factors: x={scale_x}, y={scale_y}")
    
    # Create a coverage mask to track extracted regions
    coverage_mask = np.zeros((grid_y, grid_x), dtype=bool)
    
    # Process each contour to mark tissue-containing grid cells
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        
        # Convert to WSI coordinates
        abs_x = int(x * scale_x)
        abs_y = int(y * scale_y)
        abs_w = int(w * scale_x)
        abs_h = int(h * scale_y)
        
        # Calculate grid cells that overlap with this contour
        start_x = max(0, abs_x // patch_size[0])
        end_x = min(grid_x - 1, (abs_x + abs_w) // patch_size[0])
        start_y = max(0, abs_y // patch_size[1])
        end_y = min(grid_y - 1, (abs_y + abs_h) // patch_size[1])
        
        # Mark these grid cells as containing tissue
        coverage_mask[start_y:end_y+1, start_x:end_x+1] = True
    
    # Extract patches from marked grid cells
    patch_count = 0
    tissue_patches = []
    
    # Count how many grid cells are marked as containing tissue
    tissue_cells = np.sum(coverage_mask)
    print(f"Number of grid cells containing tissue: {tissue_cells}")
    
    # Limit to 20 patches for testing
    max_patches = 20
    
    for y in range(grid_y):
        for x in range(grid_x):
            if coverage_mask[y, x]:
                # Calculate WSI coordinates for this patch
                x_patch = x * patch_size[0]
                y_patch = y * patch_size[1]
                
                # Ensure we don't go out of bounds
                if x_patch + patch_size[0] > slide.dimensions[0]:
                    x_patch = slide.dimensions[0] - patch_size[0]
                if y_patch + patch_size[1] > slide.dimensions[1]:
                    y_patch = slide.dimensions[1] - patch_size[1]
                
                try:
                    # Extract the patch
                    print(f"Extracting patch at ({x_patch}, {y_patch})...")
                    patch = slide.read_region((x_patch, y_patch), 0, patch_size).convert("RGB")
                    patch_np = np.array(patch)
                    
                    # Verify tissue content (exclude mostly white patches)
                    mean_intensity = np.mean(patch_np)
                    print(f"Patch mean intensity: {mean_intensity}")
                    
                    if mean_intensity < 230:  # Simple intensity check
                        tissue_patches.append(patch_np)
                        patch_filename = os.path.join(output_dir, f"tissue_patch_{patch_count}.png")
                        
                        # Save the patch
                        patch.save(patch_filename)
                        print(f"Saved patch {patch_count} to {patch_filename}")
                        
                        patch_count += 1
                        
                        # Limit to max_patches
                        if patch_count >= max_patches:
                            print(f"Reached maximum patch count of {max_patches}")
                            break
                except Exception as e:
                    print(f"Error processing patch at ({x_patch}, {y_patch}): {str(e)}")
                    continue
        
        if patch_count >= max_patches:
            break
    
    print(f"Total non-overlapping tissue patches saved: {patch_count}")
    return tissue_patches, patch_count

# Main execution
try:
    print(f"Testing patch extraction on: {wsi_path}")
    
    # Check if the file exists
    if not os.path.exists(wsi_path):
        print(f"ERROR: File not found: {wsi_path}")
        exit(1)
    
    # Load the WSI using OpenSlide
    print("Loading slide with OpenSlide...")
    slide = openslide.OpenSlide(wsi_path)
    print(f"Slide dimensions: {slide.dimensions}")
    
    # Define target patch size
    target_width, target_height = 1080, 566  # Target size for output patches
    patch_size = (target_width, target_height)
    
    # Read a downsampled thumbnail for quick processing
    thumbnail_size = (2048, 2048)  # Smaller thumbnail for faster processing
    print("Generating thumbnail...")
    thumbnail = slide.get_thumbnail(thumbnail_size)
    thumbnail = thumbnail.convert("RGB")
    print(f"Thumbnail size: {thumbnail.size}")
    
    # Save the thumbnail for inspection
    thumbnail_path = os.path.join(output_dir, "thumbnail.png")
    thumbnail.save(thumbnail_path)
    print(f"Saved thumbnail to {thumbnail_path}")
    
    # Convert thumbnail to NumPy array for processing
    wsi_image_np = np.array(thumbnail)
    
    # Apply Reinhard normalization
    print("Applying Reinhard normalization...")
    normalized_wsi = reinhard_normalization(wsi_image_np)
    
    # Save normalized image for inspection
    normalized_path = os.path.join(output_dir, "normalized.png")
    Image.fromarray(normalized_wsi).save(normalized_path)
    print(f"Saved normalized image to {normalized_path}")
    
    # Get tissue mask
    print("Generating tissue mask...")
    tissue_mask = optimized_tissue_detection(normalized_wsi)
    
    # Save tissue mask for inspection
    mask_path = os.path.join(output_dir, "tissue_mask.png")
    Image.fromarray(tissue_mask).save(mask_path)
    print(f"Saved tissue mask to {mask_path}")
    
    # Find contours of tissue regions
    print("Finding tissue contours...")
    contours, _ = cv2.findContours(tissue_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    print(f"Found {len(contours)} tissue regions")
    
    # Extract non-overlapping patches
    start_time = time.time()
    tissue_patches, patch_count = extract_non_overlapping_patches(
        slide, contours, thumbnail_size, patch_size, output_dir
    )
    end_time = time.time()
    
    print(f"Patch extraction time: {end_time - start_time:.2f} seconds")
    
    # Display success message
    if patch_count > 0:
        print(f"SUCCESS: Extracted {patch_count} patches")
    else:
        print("WARNING: No patches were extracted")
    
except Exception as e:
    import traceback
    print(f"ERROR: {str(e)}")
    print(traceback.format_exc())
