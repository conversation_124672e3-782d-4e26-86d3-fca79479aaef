import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { Loader2, Upload } from 'lucide-react';

interface WholeSlideImageUploaderProps {
  onPatchesExtracted: (patches: File[]) => void;
}

// Function to create a colored canvas and return as a blob (fallback if server fails)
const createColoredPatch = (color: string, text: string, size = 300): Promise<Blob> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d');

    if (ctx) {
      // Fill background
      ctx.fillStyle = color;
      ctx.fillRect(0, 0, size, size);

      // Add text
      ctx.fillStyle = 'white';
      ctx.font = 'bold 24px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(text, size / 2, size / 2);

      // Convert to blob
      canvas.toBlob((blob) => {
        resolve(blob as Blob);
      }, 'image/png');
    } else {
      // Fallback if canvas context is not available
      const svgString = `
        <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}">
          <rect width="100%" height="100%" fill="${color}" />
          <text x="50%" y="50%" font-family="Arial" font-size="24" fill="white" text-anchor="middle" dominant-baseline="middle">${text}</text>
        </svg>
      `;
      const blob = new Blob([svgString], { type: 'image/svg+xml' });
      resolve(blob);
    }
  });
};

const CHUNK_SIZE = 1024 * 1024; // 1MB chunks

const WholeSlideImageUploader: React.FC<WholeSlideImageUploaderProps> = ({ onPatchesExtracted }) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [serverAvailable, setServerAvailable] = useState<boolean | null>(null);

  // Check if the server is available on component mount
  useEffect(() => {
    const checkServer = async () => {
      try {
        console.log('Checking server availability...');

        // Try multiple endpoints to ensure connectivity
        const endpoints = [
          '/api/test'
        ];

        const tryEndpoint = async (endpoint: string) => {
          try {
            console.log(`Trying endpoint: ${endpoint}`);

            const response = await fetch(endpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
              },
              mode: 'cors',
              cache: 'no-cache',
            });

            if (response.ok) {
              const data = await response.json();
              console.log(`Server response from ${endpoint}:`, data);
              return { isConnected: true, data };
            }
            console.log(`Endpoint ${endpoint} returned status: ${response.status}`);
          } catch (endpointError) {
            console.error(`Error connecting to ${endpoint}:`, endpointError);
          }
          return null;
        };

        // Try each endpoint until one succeeds
        for (const endpoint of endpoints) {
          const result = await tryEndpoint(endpoint);
          if (result) {
            setServerAvailable(true);
            console.log('Server status: available');
            if (result.data) {
              console.log('Server response data:', result.data);
            }
            toast.success('Connected to WSI processing server');
            return;
          }
        }

        // If we get here, no endpoints were successful
        setServerAvailable(false);
        console.log('Server status: unavailable');

      } catch (error) {
        console.error('Server check failed:', error);
        setServerAvailable(false);
      }
    };

    // Initial check
    checkServer();

    // Check server every 5 seconds in case it starts later
    const interval = setInterval(checkServer, 5000);

    return () => clearInterval(interval);
  }, []);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      setSelectedFile(files[0]);
    }
  };

  const uploadChunk = async (
    chunk: Blob,
    chunkNumber: number,
    totalChunks: number,
    filename: string,
    currentSessionId: string | null
  ) => {
    try {
      const formData = new FormData();
      formData.append('chunk', chunk);
      formData.append('chunkNumber', chunkNumber.toString());
      formData.append('totalChunks', totalChunks.toString());
      formData.append('filename', filename);
      if (currentSessionId) {
        formData.append('sessionId', currentSessionId);
      }

      console.log(`Uploading chunk ${chunkNumber + 1}/${totalChunks}`);

      const response = await fetch('/api/upload-chunk', {
        method: 'POST',
        body: formData,
        headers: {
          'Accept': 'application/json'
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Chunk upload failed: ${response.status}`, errorText);
        throw new Error(`Upload failed: ${response.statusText}. ${errorText}`);
      }

      const data = await response.json();
      console.log(`Chunk ${chunkNumber + 1}/${totalChunks} uploaded successfully`);
      return data;
    } catch (error) {
      console.error(`Error uploading chunk ${chunkNumber + 1}/${totalChunks}:`, error);
      throw error;
    }
  };

  const handleFileUpload = async (file: File) => {
    try {
      setIsUploading(true);
      setUploadProgress(0);

      const totalChunks = Math.ceil(file.size / CHUNK_SIZE);
      let currentSessionId = null;

      for (let chunkNumber = 0; chunkNumber < totalChunks; chunkNumber++) {
        const start = chunkNumber * CHUNK_SIZE;
        const end = Math.min(start + CHUNK_SIZE, file.size);
        const chunk = file.slice(start, end);

        const response = await uploadChunk(chunk, chunkNumber, totalChunks, file.name, currentSessionId);
        
        // Save session ID from first chunk response
        if (!currentSessionId && response.sessionId) {
          currentSessionId = response.sessionId;
          setSessionId(currentSessionId);
        }

        // Update progress
        const progress = ((chunkNumber + 1) / totalChunks) * 100;
        setUploadProgress(progress);

        // If all chunks are uploaded and processed
        if (response.patches) {
          onPatchesExtracted(response.patches);
          toast.success(`Successfully extracted ${response.patchesCount} patches`);
          break;
        }
      }
    } catch (error) {
      console.error('Upload error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to upload file');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const processWholeSlideImage = async () => {
    if (!selectedFile) {
      toast.error('Please select a whole slide image file first');
      return;
    }

    // Force check if server is available with multiple endpoints
    let serverIsAvailable = false;
    let workingEndpoint = '';

    const endpoints = [
      '/api/test'
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`Testing server endpoint: ${endpoint}`);
        const testResponse = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
          mode: 'cors',
          cache: 'no-cache',
        });

        if (testResponse.ok) {
          serverIsAvailable = true;
          workingEndpoint = endpoint;
          console.log(`Server available at ${workingEndpoint}`);
          break;
        }
      } catch (error) {
        console.error(`Server test failed for ${endpoint}:`, error);
      }
    }

    // Update the state
    setServerAvailable(serverIsAvailable);

    // If server is not available, show error
    if (!serverIsAvailable) {
      toast.error('Server is not available. Please ensure the backend server is running and try again.');
      return;
    }

    // Continue with server-side processing
    setIsUploading(true);
    setUploadProgress(0);

    const formData = new FormData();
    formData.append('file', selectedFile);

    try {
      // Simulate upload progress
      const uploadInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + 5;
          if (newProgress >= 95) {
            clearInterval(uploadInterval);
            return 95;
          }
          return newProgress;
        });
      }, 200);

      console.log('Uploading whole slide image:', selectedFile.name, 'Size:', selectedFile.size);
      console.log('Using server endpoint:', workingEndpoint);

      // Upload the file to the server with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 120000); // 120 second timeout for large files

      const uploadUrl = `/api/upload-wsi`;
      console.log(`Uploading to: ${uploadUrl}`);

      let response;
      try {
        response = await fetch(uploadUrl, {
          method: 'POST',
          body: formData,
          signal: controller.signal,
          headers: {
            'Accept': 'application/json'
          }
        });
      } catch (error) {
        console.error('Network error during upload:', error);
        throw new Error('Failed to connect to server. Please check if the server is running.');
      }

      clearTimeout(timeoutId);
      clearInterval(uploadInterval);
      setUploadProgress(100);

      console.log('Upload response status:', response.status);
      console.log('Upload response headers:', response.headers);

      if (!response.ok) {
        let errorText = '';
        try {
          errorText = await response.text();
        } catch (e) {
          errorText = 'Could not read error response';
        }
        console.error('Server error:', response.status, errorText);
        throw new Error(`Server error: ${response.status}. ${errorText}`);
      }

      let data;
      try {
        data = await response.json();
        console.log('Server response:', data);
      } catch (e) {
        console.error('Error parsing response JSON:', e);
        throw new Error('Invalid response from server. Could not parse JSON.');
      }

      setIsUploading(false);

      if (data.success) {
        toast.success(`Successfully processed whole slide image. Extracted ${data.patchesCount} patches.`);

        // Download all the patches as File objects
        setIsProcessing(true);
        setProcessingProgress(0);

        const patchFiles: File[] = [];
        const totalPatches = data.patches.length;

        for (let i = 0; i < totalPatches; i++) {
          const patch = data.patches[i];
          const patchUrl = `/api/patches/${data.sessionId}/${patch.filename}`;

          try {
            console.log(`Downloading patch ${i+1}/${totalPatches}: ${patch.filename}`);
            const patchResponse = await fetch(patchUrl, {
              method: 'GET',
              headers: {
                'Accept': 'image/png'
              },
              mode: 'cors',
              cache: 'no-cache',
            });

            if (!patchResponse.ok) {
              console.error(`Failed to download patch ${patch.filename}, status: ${patchResponse.status}`);
              continue;
            }

            const blob = await patchResponse.blob();
            const file = new File([blob], patch.filename, { type: 'image/png' });
            patchFiles.push(file);

            // Update progress
            setProcessingProgress(Math.round(((i + 1) / totalPatches) * 100));
          } catch (error) {
            console.error(`Failed to download patch ${patch.filename}:`, error);
          }
        }

        setIsProcessing(false);

        if (patchFiles.length > 0) {
          // Clean up the session on the server
          fetch(`/api/cleanup/${data.sessionId}`, {
            method: 'DELETE',
            mode: 'cors',
            cache: 'no-cache',
          }).catch(error => {
            console.error('Failed to clean up session:', error);
          });

          // Pass the patch files to the parent component
          onPatchesExtracted(patchFiles);
          toast.success(`Successfully extracted ${patchFiles.length} patches from the whole slide image.`);
        } else {
          throw new Error('Failed to download any patches from the server.');
        }
      } else {
        throw new Error(data.error || 'Server did not indicate success');
      }
    } catch (error) {
      console.error('Error processing whole slide image:', error);
      toast.error(`Failed to process whole slide image: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsUploading(false);
      setIsProcessing(false);
    }
  };

  // Fallback method to generate sample patches client-side
  const generateSamplePatches = async () => {
    setIsUploading(false);
    setIsProcessing(true);
    setProcessingProgress(0);

    try {
      const patchFiles: File[] = [];
      const colors = [
        '#FF5252', // Red for tumor
        '#4DA6FF', // Blue for stroma
        '#4CAF50', // Green for vessel
        '#FFC107', // Yellow
        '#9C27B0'  // Purple
      ];

      const totalPatches = 10; // Generate 10 sample patches

      for (let i = 0; i < totalPatches; i++) {
        try {
          // Create a colored patch
          const colorIndex = i % colors.length;
          const blob = await createColoredPatch(
            colors[colorIndex],
            `Patch ${i+1}`,
            300
          );

          // Create a File object
          const file = new File([blob], `patch_${i}.png`, { type: 'image/png' });
          patchFiles.push(file);

          // Update progress
          setProcessingProgress(Math.round(((i + 1) / totalPatches) * 100));

          // Add a small delay to simulate processing time
          await new Promise(resolve => setTimeout(resolve, 200));
        } catch (error) {
          console.error(`Failed to generate patch ${i}:`, error);
        }
      }

      setIsProcessing(false);

      if (patchFiles.length > 0) {
        // Pass the patch files to the parent component
        onPatchesExtracted(patchFiles);
      } else {
        throw new Error('Failed to generate any patches.');
      }
    } catch (error) {
      console.error('Error generating sample patches:', error);
      toast.error(`Failed to generate patches: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsProcessing(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Whole Slide Image Analysis</CardTitle>
        <CardDescription>
          Upload a whole slide image to extract and analyze tissue patches
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col gap-4">
          <div className="border-2 border-dashed rounded-lg p-6 text-center">
            <input
              type="file"
              id="wsi-upload"
              className="hidden"
              accept=".svs,.tif,.tiff,.ndpi,.mrxs"
              onChange={handleFileChange}
              disabled={isUploading || isProcessing}
            />
            <label
              htmlFor="wsi-upload"
              className="flex flex-col items-center justify-center cursor-pointer"
            >
              <Upload className="h-10 w-10 text-gray-400 mb-2" />
              <p className="text-sm text-gray-600 mb-1">
                {selectedFile ? selectedFile.name : 'Click to select a whole slide image'}
              </p>
              <p className="text-xs text-gray-400">
                Supported formats: .svs, .tif, .tiff, .ndpi, .mrxs
              </p>
            </label>
          </div>

          {selectedFile && (
            <div className="space-y-2">
              <p className="text-sm font-medium">Selected file: {selectedFile.name}</p>
              <p className="text-xs text-gray-500">Size: {(selectedFile.size / (1024 * 1024)).toFixed(2)} MB</p>
            </div>
          )}

          {serverAvailable === false && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-700">
                Server is not available. Patches will be generated client-side.
              </p>
            </div>
          )}

          {isUploading && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Uploading and processing image...</span>
                <span>{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="h-2" />
            </div>
          )}

          {isProcessing && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Extracting patches...</span>
                <span>{processingProgress}%</span>
              </div>
              <Progress value={processingProgress} className="h-2" />
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button
          onClick={processWholeSlideImage}
          disabled={!selectedFile || isUploading || isProcessing}
          className="w-full"
        >
          {(isUploading || isProcessing) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isUploading ? 'Uploading...' : isProcessing ? 'Extracting...' : 'Extract Patches from WSI'}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default WholeSlideImageUploader;
