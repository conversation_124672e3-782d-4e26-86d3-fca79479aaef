<svg width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#A855F7;stop-opacity:0.2" />
    </linearGradient>
    <linearGradient id="screenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3a8a" />
      <stop offset="100%" style="stop-color:#0EA5E9" />
    </linearGradient>
    <linearGradient id="speedGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#8B5CF6" />
      <stop offset="50%" style="stop-color:#A855F7" />
      <stop offset="100%" style="stop-color:#EC4899" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="400" fill="url(#bgGradient3)"/>
  
  <!-- Speed Pattern -->
  <pattern id="speedPattern" x="0" y="0" width="80" height="40" patternUnits="userSpaceOnUse">
    <path d="M0 20 L20 20" stroke="#8B5CF6" stroke-width="1" opacity="0.1"/>
    <path d="M30 20 L50 20" stroke="#8B5CF6" stroke-width="1" opacity="0.1"/>
    <path d="M60 20 L80 20" stroke="#8B5CF6" stroke-width="1" opacity="0.1"/>
  </pattern>
  <rect width="600" height="400" fill="url(#speedPattern)"/>
  
  <!-- Computer/Monitor -->
  <g transform="translate(150, 100)">
    <!-- Monitor Base -->
    <rect x="0" y="180" width="300" height="20" rx="10" fill="#64748B" opacity="0.6"/>
    <rect x="130" y="160" width="40" height="20" fill="#64748B" opacity="0.6"/>
    
    <!-- Monitor Screen -->
    <rect x="0" y="0" width="300" height="180" rx="15" fill="url(#screenGradient)" opacity="0.9"/>
    <rect x="15" y="15" width="270" height="150" rx="10" fill="#000000" opacity="0.8"/>
    
    <!-- Screen Content -->
    <g transform="translate(25, 25)">
      <!-- Header -->
      <rect x="0" y="0" width="250" height="25" fill="#0EA5E9" opacity="0.8"/>
      <text x="125" y="16" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Real-Time Analysis</text>
      
      <!-- Progress Indicator -->
      <g transform="translate(0, 35)">
        <text x="0" y="15" fill="#10B981" font-family="Arial, sans-serif" font-size="10">Processing: Complete ✓</text>
        <rect x="0" y="20" width="250" height="8" rx="4" fill="#1e3a8a"/>
        <rect x="0" y="20" width="250" height="8" rx="4" fill="#10B981"/>
        
        <!-- Time Indicator -->
        <text x="250" y="15" text-anchor="end" fill="#0EA5E9" font-family="Arial, sans-serif" font-size="10" font-weight="bold">1.2 seconds</text>
      </g>
      
      <!-- Results Grid -->
      <g transform="translate(0, 65)">
        <!-- Result Card 1 -->
        <rect x="0" y="0" width="75" height="60" rx="5" fill="#10B981" opacity="0.2" stroke="#10B981" stroke-width="1"/>
        <text x="37.5" y="15" text-anchor="middle" fill="#10B981" font-family="Arial, sans-serif" font-size="8">Stroma</text>
        <text x="37.5" y="35" text-anchor="middle" fill="#10B981" font-family="Arial, sans-serif" font-size="16" font-weight="bold">65%</text>
        <text x="37.5" y="50" text-anchor="middle" fill="#10B981" font-family="Arial, sans-serif" font-size="7">Normal</text>
        
        <!-- Result Card 2 -->
        <rect x="87.5" y="0" width="75" height="60" rx="5" fill="#F59E0B" opacity="0.2" stroke="#F59E0B" stroke-width="1"/>
        <text x="125" y="15" text-anchor="middle" fill="#F59E0B" font-family="Arial, sans-serif" font-size="8">Tumor</text>
        <text x="125" y="35" text-anchor="middle" fill="#F59E0B" font-family="Arial, sans-serif" font-size="16" font-weight="bold">25%</text>
        <text x="125" y="50" text-anchor="middle" fill="#F59E0B" font-family="Arial, sans-serif" font-size="7">Detected</text>
        
        <!-- Result Card 3 -->
        <rect x="175" y="0" width="75" height="60" rx="5" fill="#8B5CF6" opacity="0.2" stroke="#8B5CF6" stroke-width="1"/>
        <text x="212.5" y="15" text-anchor="middle" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="8">Vessel</text>
        <text x="212.5" y="35" text-anchor="middle" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="16" font-weight="bold">10%</text>
        <text x="212.5" y="50" text-anchor="middle" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="7">Normal</text>
      </g>
    </g>
  </g>
  
  <!-- Speed Indicators -->
  <g transform="translate(50, 50)" opacity="0.7">
    <!-- Speed Lines -->
    <g stroke="url(#speedGradient)" stroke-width="3" opacity="0.6">
      <path d="M0 20 L40 20"/>
      <path d="M0 30 L50 30"/>
      <path d="M0 40 L35 40"/>
      <path d="M0 50 L45 50"/>
    </g>
    
    <!-- Speed Text -->
    <text x="0" y="15" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="12" font-weight="bold">INSTANT</text>
  </g>
  
  <!-- Clock/Timer -->
  <g transform="translate(480, 80)">
    <!-- Clock Face -->
    <circle cx="40" cy="40" r="35" fill="rgba(255,255,255,0.9)" stroke="#8B5CF6" stroke-width="3"/>
    <circle cx="40" cy="40" r="30" fill="none" stroke="#8B5CF6" stroke-width="1" opacity="0.3"/>
    
    <!-- Clock Numbers -->
    <text x="40" y="20" text-anchor="middle" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="10">12</text>
    <text x="60" y="45" text-anchor="middle" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="10">3</text>
    <text x="40" y="65" text-anchor="middle" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="10">6</text>
    <text x="20" y="45" text-anchor="middle" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="10">9</text>
    
    <!-- Clock Hands -->
    <line x1="40" y1="40" x2="40" y2="25" stroke="#8B5CF6" stroke-width="2"/>
    <line x1="40" y1="40" x2="50" y2="40" stroke="#8B5CF6" stroke-width="2"/>
    <circle cx="40" cy="40" r="3" fill="#8B5CF6"/>
    
    <!-- Timer Text -->
    <text x="40" y="95" text-anchor="middle" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="10" font-weight="bold">&lt; 2 min</text>
  </g>
  
  <!-- Data Flow Animation -->
  <g transform="translate(100, 320)" opacity="0.6">
    <!-- Data Packets -->
    <rect x="0" y="0" width="15" height="10" rx="2" fill="#0EA5E9"/>
    <rect x="25" y="0" width="15" height="10" rx="2" fill="#10B981"/>
    <rect x="50" y="0" width="15" height="10" rx="2" fill="#8B5CF6"/>
    <rect x="75" y="0" width="15" height="10" rx="2" fill="#F59E0B"/>
    
    <!-- Flow Arrow -->
    <path d="M100 5 L130 5" stroke="#8B5CF6" stroke-width="2" marker-end="url(#flowArrow)"/>
    
    <!-- Processing Box -->
    <rect x="140" y="-5" width="60" height="20" rx="5" fill="rgba(139, 92, 246, 0.2)" stroke="#8B5CF6" stroke-width="1"/>
    <text x="170" y="8" text-anchor="middle" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="8">AI Engine</text>
    
    <!-- Output Arrow -->
    <path d="M210 5 L240 5" stroke="#8B5CF6" stroke-width="2" marker-end="url(#flowArrow)"/>
    
    <!-- Results -->
    <rect x="250" y="-5" width="50" height="20" rx="5" fill="rgba(16, 185, 129, 0.2)" stroke="#10B981" stroke-width="1"/>
    <text x="275" y="8" text-anchor="middle" fill="#10B981" font-family="Arial, sans-serif" font-size="8">Results</text>
  </g>
  
  <!-- Arrow Marker for Flow -->
  <defs>
    <marker id="flowArrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#8B5CF6"/>
    </marker>
  </defs>
  
  <!-- Performance Metrics -->
  <g transform="translate(350, 320)">
    <rect x="0" y="0" width="200" height="60" rx="10" fill="rgba(255,255,255,0.9)" stroke="#8B5CF6" stroke-width="2"/>
    <text x="100" y="20" text-anchor="middle" fill="#1e3a8a" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Performance Metrics</text>
    
    <g transform="translate(20, 30)">
      <text x="0" y="0" fill="#10B981" font-family="Arial, sans-serif" font-size="9">• Accuracy: 96%</text>
      <text x="80" y="0" fill="#0EA5E9" font-family="Arial, sans-serif" font-size="9">• Speed: 1.2s</text>
      <text x="0" y="15" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="9">• Reliability: 99.2%</text>
      <text x="80" y="15" fill="#F59E0B" font-family="Arial, sans-serif" font-size="9">• Real-time</text>
    </g>
  </g>
</svg>
