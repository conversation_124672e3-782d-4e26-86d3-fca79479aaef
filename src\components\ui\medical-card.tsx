import { cn } from "@/lib/utils";
import React from "react";

interface MedicalCardProps extends React.HTMLAttributes<HTMLDivElement> {
  gradient?: boolean;
}

export const MedicalCard = React.forwardRef<HTMLDivElement, MedicalCardProps>(
  ({ className, gradient = false, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "bg-white rounded-xl p-6 shadow-lg transition-all duration-300 hover:shadow-xl",
          gradient && "bg-gradient-to-br from-sky-blue/10 via-medical-mint/10 to-lavender/10",
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);
