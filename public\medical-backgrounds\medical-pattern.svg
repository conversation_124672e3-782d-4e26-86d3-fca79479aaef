<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="medical-grid" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
      <circle cx="10" cy="10" r="1" fill="#E0F2FE" opacity="0.3"/>
      <path d="M8 10h4M10 8v4" stroke="#0EA5E9" stroke-width="0.5" opacity="0.2"/>
    </pattern>
    <pattern id="dna-helix" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M5 5Q15 15 25 5Q35 15 45 5" stroke="#7DD3FC" stroke-width="1" fill="none" opacity="0.1"/>
      <path d="M5 25Q15 35 25 25Q35 35 45 25" stroke="#7DD3FC" stroke-width="1" fill="none" opacity="0.1"/>
      <circle cx="10" cy="10" r="1.5" fill="#0EA5E9" opacity="0.15"/>
      <circle cx="30" cy="10" r="1.5" fill="#0EA5E9" opacity="0.15"/>
      <circle cx="20" cy="30" r="1.5" fill="#0EA5E9" opacity="0.15"/>
    </pattern>
  </defs>
  <rect width="100" height="100" fill="url(#medical-grid)"/>
  <rect width="100" height="100" fill="url(#dna-helix)"/>
</svg>
