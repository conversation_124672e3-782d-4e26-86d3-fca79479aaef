export interface ClinicalMatrices {
  tsr: number; // Tumor-Stroma Ratio
  tvr: number; // Tumor-Vessel Ratio
  svr: number; // Stroma-Vessel Ratio
  tsrStatus: 'Good' | 'Poor';
  tvrStatus: 'Good' | 'Poor';
  svrStatus: 'Good' | 'Poor';
  overallPrognosis: 'Good' | 'Poor';
  confidenceScore: number;
  prognosticFactors: Array<{
    description: string;
    impact: 'positive' | 'negative';
  }>;
  recommendations: string[];
}

export interface PrognosisThresholds {
  tsr: number;
  tvr: number;
  svr: number;
}

export const DEFAULT_THRESHOLDS: PrognosisThresholds = {
  tsr: 0.5,  // Above this (>50% tumor) indicates poor prognosis
  tvr: 2.0,  // Above this indicates poor prognosis
  svr: 3.0   // Above this indicates poor prognosis
};
