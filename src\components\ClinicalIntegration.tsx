
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import {
  calculateClinicalMatrices,
  getTSRInterpretation,
  getTVRInterpretation,
  getSVRInterpretation,
  type AnalysisResult,
  type ClinicalMatrices
} from '../lib/modelService';

interface PatientInfo {
  patientId: string;
  age: number;
  stage: string;
  histologyGrade: number;
}

interface ClinicalIntegrationProps {
  analysisResults: AnalysisResult[];
}

const ClinicalIntegration: React.FC<ClinicalIntegrationProps> = ({ analysisResults }) => {
  const [patientInfo, setPatientInfo] = useState<PatientInfo>({
    patientId: '',
    age: 0,
    stage: '',
    histologyGrade: 0
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [integrationComplete, setIntegrationComplete] = useState(false);
  const [clinicalReport, setClinicalReport] = useState<string | null>(null);
  const [matrices, setMatrices] = useState<ClinicalMatrices | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPatientInfo(prev => ({
      ...prev,
      [name]: name === 'age' || name === 'histologyGrade' ? Number(value) : value
    }));
  };

  const processIntegration = () => {
    if (!analysisResults.length) {
      toast.error("No analysis results to integrate");
      return;
    }

    if (!patientInfo.patientId) {
      toast.error("Please enter a patient ID");
      return;
    }

    setIsProcessing(true);

    // Calculate averages
    const avgStroma = Math.round(analysisResults.reduce((sum, r) => sum + r.stroma, 0) / analysisResults.length);
    const avgTumor = Math.round(analysisResults.reduce((sum, r) => sum + r.tumor, 0) / analysisResults.length);
    const avgVessel = Math.round(analysisResults.reduce((sum, r) => sum + r.vessel, 0) / analysisResults.length);

    // Calculate clinical matrices
    const clinicalMatrices = calculateClinicalMatrices(analysisResults);
    setMatrices(clinicalMatrices);

    // Generate clinical report
    const report = generateClinicalReport(
      avgStroma,
      avgTumor,
      avgVessel,
      patientInfo,
      clinicalMatrices
    );

    // Simulate processing delay
    setTimeout(() => {
      setClinicalReport(report);
      setIntegrationComplete(true);
      setIsProcessing(false);
      toast.success("Clinical integration complete");
    }, 1000);
  };

  const generateClinicalReport = (
    stroma: number,
    tumor: number,
    vessel: number,
    patient: PatientInfo,
    matrices: ClinicalMatrices
  ) => {
    // This function is no longer needed as we're using the new visual layout
    // But keeping it to avoid breaking the existing call
    return "Clinical report generated successfully";
  };

  const resetForm = () => {
    setPatientInfo({
      patientId: '',
      age: 0,
      stage: '',
      histologyGrade: 0
    });
    setIntegrationComplete(false);
    setClinicalReport(null);
    setMatrices(null);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Clinical Matrix Integration</CardTitle>
      </CardHeader>
      <CardContent>
        {!integrationComplete ? (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="patientId">Patient ID</Label>
                <Input
                  id="patientId"
                  name="patientId"
                  placeholder="Enter patient ID"
                  value={patientInfo.patientId}
                  onChange={handleInputChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="age">Age</Label>
                <Input
                  id="age"
                  name="age"
                  type="number"
                  placeholder="Enter patient age"
                  value={patientInfo.age || ''}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="stage">FIGO Stage</Label>
                <Input
                  id="stage"
                  name="stage"
                  placeholder="e.g., IA, IB, II"
                  value={patientInfo.stage}
                  onChange={handleInputChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="histologyGrade">Histological Grade (1-3)</Label>
                <Input
                  id="histologyGrade"
                  name="histologyGrade"
                  type="number"
                  min={1}
                  max={3}
                  placeholder="1-3"
                  value={patientInfo.histologyGrade || ''}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Clinical Analysis Header */}
            <div className="text-center pb-6 mb-8 border-b-2 border-gradient-to-r from-blue-200 to-purple-200">
              <h3 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                🏥 Clinical Analysis & Prognosis
              </h3>
              <p className="text-gray-600 mt-3 text-lg">AI-powered tissue analysis with clinical interpretation</p>
            </div>

            {/* Clinical Ratios Cards - 3 Column Grid */}
            <div className="mb-8">
              <h4 className="text-xl font-bold text-gray-800 mb-6 text-center">Clinical Ratios</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {matrices && (
                  <>
                    {/* TSR Card */}
                    <div className="bg-white rounded-xl shadow-lg border border-blue-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                      <div className="bg-gradient-to-br from-blue-500 to-blue-600 text-white p-4 rounded-t-xl">
                        <h5 className="font-semibold text-lg">Tumor-Stroma Ratio</h5>
                        <p className="text-blue-100 text-sm">(TSR)</p>
                      </div>
                      <div className="p-6">
                        <div className="text-center mb-4">
                          <div className="text-4xl font-bold text-blue-600 mb-2">
                            {(matrices.tsr * 100).toFixed(1)}%
                          </div>
                          <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                            matrices.tsr < 0.5 ? 'bg-red-100 text-red-700' :
                            matrices.tsr < 0.7 ? 'bg-yellow-100 text-yellow-700' :
                            'bg-green-100 text-green-700'
                          }`}>
                            {matrices.tsr < 0.5 ? 'Poor' : matrices.tsr < 0.7 ? 'Fair' : 'Good'}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 text-center leading-relaxed">
                          {getTSRInterpretation(matrices.tsr)}
                        </p>
                      </div>
                    </div>

                    {/* TVR Card */}
                    <div className="bg-white rounded-xl shadow-lg border border-red-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                      <div className="bg-gradient-to-br from-red-500 to-red-600 text-white p-4 rounded-t-xl">
                        <h5 className="font-semibold text-lg">Tumor-Vessel Ratio</h5>
                        <p className="text-red-100 text-sm">(TVR)</p>
                      </div>
                      <div className="p-6">
                        <div className="text-center mb-4">
                          <div className="text-4xl font-bold text-red-600 mb-2">
                            {matrices.tvr.toFixed(2)}
                          </div>
                          <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                            matrices.tvr < 2 ? 'bg-red-100 text-red-700' :
                            matrices.tvr < 4 ? 'bg-yellow-100 text-yellow-700' :
                            'bg-green-100 text-green-700'
                          }`}>
                            {matrices.tvr < 2 ? 'Poor' : matrices.tvr < 4 ? 'Fair' : 'Good'}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 text-center leading-relaxed">
                          {getTVRInterpretation(matrices.tvr)}
                        </p>
                      </div>
                    </div>

                    {/* SVR Card */}
                    <div className="bg-white rounded-xl shadow-lg border border-green-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                      <div className="bg-gradient-to-br from-green-500 to-green-600 text-white p-4 rounded-t-xl">
                        <h5 className="font-semibold text-lg">Stroma-Vessel Ratio</h5>
                        <p className="text-green-100 text-sm">(SVR)</p>
                      </div>
                      <div className="p-6">
                        <div className="text-center mb-4">
                          <div className="text-4xl font-bold text-green-600 mb-2">
                            {matrices.svr.toFixed(2)}
                          </div>
                          <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                            matrices.svr > 5 ? 'bg-red-100 text-red-700' :
                            matrices.svr > 3 ? 'bg-yellow-100 text-yellow-700' :
                            'bg-green-100 text-green-700'
                          }`}>
                            {matrices.svr > 5 ? 'Poor' : matrices.svr > 3 ? 'Fair' : 'Good'}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 text-center leading-relaxed">
                          {getSVRInterpretation(matrices.svr)}
                        </p>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Overall Prognosis Section */}
            <div className="bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 rounded-2xl p-8 shadow-lg">
              <h4 className="text-2xl font-bold text-center text-gray-800 mb-6">Overall Prognosis</h4>

              {matrices && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Left: Prognosis Summary */}
                  <div className="text-center">
                    {/* Large Prognosis Badge */}
                    <div className="mb-6">
                      <div className={`inline-flex items-center justify-center w-48 h-48 rounded-full text-white text-xl font-bold shadow-2xl ${
                        (matrices.tsr < 0.5 || matrices.tvr < 2) ? 'bg-gradient-to-br from-red-400 to-red-600' :
                        (matrices.tsr < 0.7 || matrices.tvr < 3) ? 'bg-gradient-to-br from-yellow-400 to-orange-500' :
                        'bg-gradient-to-br from-green-400 to-green-600'
                      }`}>
                        <div className="text-center">
                          <div className="text-2xl font-bold">
                            {(matrices.tsr < 0.5 || matrices.tvr < 2) ? 'Poor' :
                             (matrices.tsr < 0.7 || matrices.tvr < 3) ? 'Fair' :
                             'Good'}
                          </div>
                          <div className="text-lg">Prognosis</div>
                        </div>
                      </div>
                      <div className="mt-4">
                        <div className="text-3xl font-bold text-gray-700">
                          {((matrices.tsr < 0.5 || matrices.tvr < 2) ? 15 :
                            (matrices.tsr < 0.7 || matrices.tvr < 3) ? 65 : 85)}%
                        </div>
                        <div className="text-gray-500">Confidence</div>
                      </div>
                    </div>
                  </div>

                  {/* Right: Key Findings */}
                  <div>
                    <h5 className="text-lg font-semibold text-gray-800 mb-4">Key Findings</h5>
                    <div className="space-y-4">
                      <div className="bg-white rounded-lg p-4 shadow-sm border-l-4 border-red-400">
                        <div className="flex items-center gap-3">
                          <div className="w-3 h-3 rounded-full bg-red-500"></div>
                          <div>
                            <span className="font-semibold text-gray-700">Tumor Content:</span>
                            <div className="text-lg font-bold text-red-600">
                              {analysisResults.length > 0 ?
                                Math.round(analysisResults.reduce((sum, r) => sum + r.tumor, 0) / analysisResults.length) : 0}%
                            </div>
                            <div className="text-sm text-gray-600">
                              {matrices.tsr < 0.5 ? 'High tumor burden (>50%)' : 'Moderate tumor content'}
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="bg-white rounded-lg p-4 shadow-sm border-l-4 border-blue-400">
                        <div className="flex items-center gap-3">
                          <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                          <div>
                            <span className="font-semibold text-gray-700">Vascularization:</span>
                            <div className="text-lg font-bold text-blue-600">
                              {matrices.tvr < 2 ? 'High' : matrices.tvr < 4 ? 'Moderate' : 'Low'}
                            </div>
                            <div className="text-sm text-gray-600">
                              TVR: {matrices.tvr.toFixed(2)} - {
                                matrices.tvr < 2 ? 'Increased angiogenesis' :
                                matrices.tvr < 4 ? 'Normal vascularization' :
                                'Reduced vessel density'
                              }
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="bg-white rounded-lg p-4 shadow-sm border-l-4 border-green-400">
                        <div className="flex items-center gap-3">
                          <div className="w-3 h-3 rounded-full bg-green-500"></div>
                          <div>
                            <span className="font-semibold text-gray-700">Stromal Response:</span>
                            <div className="text-lg font-bold text-green-600">
                              {matrices.svr > 5 ? 'Extensive' : matrices.svr > 3 ? 'Moderate' : 'Minimal'}
                            </div>
                            <div className="text-sm text-gray-600">
                              SVR: {matrices.svr.toFixed(2)} - {
                                matrices.svr > 5 ? 'Dense stroma, poor vascularization' :
                                matrices.svr > 3 ? 'Moderate stromal reaction' :
                                'Well-vascularized stroma'
                              }
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Clinical Recommendations */}
              <div className="mt-8 bg-white rounded-xl p-6 shadow-sm">
                <h5 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                  Clinical Recommendations
                </h5>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {matrices && matrices.tsr < 0.5 && (
                    <div className="flex items-start gap-3 p-3 bg-red-50 rounded-lg">
                      <div className="w-2 h-2 rounded-full bg-red-500 mt-2 flex-shrink-0"></div>
                      <div className="text-sm text-gray-700">
                        <span className="font-medium">High Tumor Content:</span> Consider more frequent monitoring due to elevated tumor burden
                      </div>
                    </div>
                  )}
                  {matrices && matrices.tvr < 2 && (
                    <div className="flex items-start gap-3 p-3 bg-orange-50 rounded-lg">
                      <div className="w-2 h-2 rounded-full bg-orange-500 mt-2 flex-shrink-0"></div>
                      <div className="text-sm text-gray-700">
                        <span className="font-medium">High Angiogenesis:</span> Additional vascular assessment recommended
                      </div>
                    </div>
                  )}
                  {matrices && matrices.svr > 5 && (
                    <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                      <div className="w-2 h-2 rounded-full bg-blue-500 mt-2 flex-shrink-0"></div>
                      <div className="text-sm text-gray-700">
                        <span className="font-medium">Dense Stroma:</span> Consider drug delivery challenges
                      </div>
                    </div>
                  )}
                  <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                    <div className="w-2 h-2 rounded-full bg-green-500 mt-2 flex-shrink-0"></div>
                    <div className="text-sm text-gray-700">
                      <span className="font-medium">Further Analysis:</span> Molecular marker evaluation recommended
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-end">
        {!integrationComplete ? (
          <Button
            onClick={processIntegration}
            disabled={isProcessing || analysisResults.length === 0}
          >
            {isProcessing ? 'Processing...' : 'Integrate with Analysis'}
          </Button>
        ) : (
          <div className="space-x-2">
            <Button variant="outline" onClick={resetForm}>
              New Analysis
            </Button>
            <Button>
              Export Report
            </Button>
          </div>
        )}
      </CardFooter>
    </Card>
  );
};

export default ClinicalIntegration;
