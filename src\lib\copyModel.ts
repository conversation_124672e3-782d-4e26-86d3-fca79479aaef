/**
 * Utility to copy the TensorFlow.js model files to the correct location
 */

import { toast } from 'sonner';

/**
 * Copies the TensorFlow.js model files to the public directory
 * This is a workaround for Vite's handling of static assets
 */
export async function copyModelToPublic(): Promise<boolean> {
  try {
    // Check if the model files exist in the source location
    console.log('Checking for model files...');
    
    // Try to fetch the model.json file
    try {
      const modelResponse = await fetch('/models/tfjs_model/model.json');
      if (!modelResponse.ok) {
        console.warn('Model file not found in source location');
        return false;
      }
      
      // Get the model JSON
      const modelJson = await modelResponse.json();
      console.log('Model JSON loaded:', modelJson);
      
      // Create a Blob from the model JSON
      const modelBlob = new Blob([JSON.stringify(modelJson)], { type: 'application/json' });
      
      // Store the model JSON in localStorage for direct access
      localStorage.setItem('tfjs_model_json', JSON.stringify(modelJson));
      
      // Try to fetch the weights file
      const weightsPath = '/models/tfjs_model/group1-shard1of1.bin';
      const weightsResponse = await fetch(weightsPath);
      if (!weightsResponse.ok) {
        console.warn('Weights file not found');
        return false;
      }
      
      // Get the weights as an ArrayBuffer
      const weightsArrayBuffer = await weightsResponse.arrayBuffer();
      
      // Convert ArrayBuffer to Base64 string for storage
      const weightsBase64 = arrayBufferToBase64(weightsArrayBuffer);
      
      // Store the weights in localStorage (this might be too large for localStorage in practice)
      // This is just for demonstration purposes
      try {
        localStorage.setItem('tfjs_model_weights', weightsBase64);
        console.log('Model weights stored in localStorage');
      } catch (e) {
        console.warn('Could not store weights in localStorage (probably too large):', e);
      }
      
      toast.success('Model files processed successfully');
      return true;
    } catch (error) {
      console.error('Error processing model files:', error);
      return false;
    }
  } catch (error) {
    console.error('Error in copyModelToPublic:', error);
    return false;
  }
}

/**
 * Convert an ArrayBuffer to a Base64 string
 */
function arrayBufferToBase64(buffer: ArrayBuffer): string {
  let binary = '';
  const bytes = new Uint8Array(buffer);
  const len = bytes.byteLength;
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return window.btoa(binary);
}

/**
 * Load the model from localStorage
 */
export async function loadModelFromLocalStorage(): Promise<tf.LayersModel | null> {
  try {
    const modelJson = localStorage.getItem('tfjs_model_json');
    if (!modelJson) {
      console.warn('No model JSON found in localStorage');
      return null;
    }
    
    // Parse the model JSON
    const modelConfig = JSON.parse(modelJson);
    
    // Create a model from the config
    const model = await tf.models.modelFromJSON(modelConfig);
    console.log('Model loaded from localStorage:', model);
    
    return model;
  } catch (error) {
    console.error('Error loading model from localStorage:', error);
    return null;
  }
}
