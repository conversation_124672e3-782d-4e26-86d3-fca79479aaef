from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import uuid
import numpy as np
from PIL import Image
import shutil
import traceback
import io
import base64
import json
import openslide

app = Flask(__name__)
# Configure Flask for larger file uploads (1GB)
app.config['MAX_CONTENT_LENGTH'] = 1024 * 1024 * 1024  # 1 GB max upload
app.config['UPLOAD_CHUNK_SIZE'] = 1024 * 1024  # 1 MB chunks
# Allow all localhost ports for development
CORS(app, resources={r"/*": {"origins": [
    "http://localhost:5173",
    "http://localhost:5174",
    "http://localhost:5175",
    "http://localhost:5176",
    "http://localhost:5177",
    "http://localhost:5178",
    "http://localhost:5179",
    "http://127.0.0.1:5173",
    "http://127.0.0.1:5174",
    "http://127.0.0.1:5175",
    "http://127.0.0.1:5176",
    "http://127.0.0.1:5177",
    "http://127.0.0.1:5178",
    "http://127.0.0.1:5179"
], "methods": ["GET", "POST", "OPTIONS", "PUT", "DELETE"], "max_age": 3600}})

# Run the server on port 8080
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080, debug=True)

# Configure upload folder
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
PATCHES_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'patches')

# Create directories if they don't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(PATCHES_FOLDER, exist_ok=True)

@app.route('/api/test', methods=['GET'])
def test_endpoint():
    """Test endpoint to check server availability"""
    return jsonify({'status': 'ok', 'message': 'Server is running'})

@app.route('/api/upload-wsi', methods=['POST'])
def upload_wsi():
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400

    file = request.files['file']

    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400

    # Generate a unique ID for this upload
    session_id = str(uuid.uuid4())
    session_folder = os.path.join(PATCHES_FOLDER, session_id)
    os.makedirs(session_folder, exist_ok=True)

    # Save the uploaded file
    file_path = os.path.join(UPLOAD_FOLDER, f"{session_id}_{file.filename}")
    file.save(file_path)

    try:
        # Check file size
        file_size = os.path.getsize(file_path)
        print(f"File size: {file_size / (1024 * 1024):.2f} MB")

        if file_size > app.config['MAX_CONTENT_LENGTH']:
            raise Exception("File too large. Maximum file size is 1 GB.")

        # Initialize WSI processor
        from wsi_processor import WSIProcessor
        processor = WSIProcessor(UPLOAD_FOLDER, PATCHES_FOLDER)
        
        # Process the WSI and extract patches
        result = processor.process_wsi(file)
        
        if not result['success']:
            raise Exception(result['error'])
            
        patches_info = result['patches']

        # Ensure we have at least one patch
        if not patches_info or len(patches_info) == 0:
            raise Exception("No valid patches could be extracted from the image.")

        response_data = {
            'success': True,
            'sessionId': session_id,
            'patchesCount': len(patches_info),
            'patches': patches_info
        }

        print(f"Returning response with {len(patches_info)} patches")
        return jsonify(response_data)
    except Exception as e:
        error_traceback = traceback.format_exc()
        print(f"Error processing image: {str(e)}")
        print(error_traceback)

        # Clean up any files that might have been created
        try:
            if os.path.exists(session_folder):
                shutil.rmtree(session_folder)
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception as cleanup_error:
            print(f"Error during cleanup: {str(cleanup_error)}")

        error_response = {'error': str(e), 'traceback': error_traceback}
        return jsonify(error_response), 500

@app.route('/api/patches/<session_id>/<patch_filename>', methods=['GET'])
def get_patch(session_id, patch_filename):
    patch_folder = os.path.join(PATCHES_FOLDER, session_id)
    return send_from_directory(patch_folder, patch_filename)

def extract_patches_with_openslide(wsi_path, output_dir):
    """
    Extract patches from a whole slide image using OpenSlide.
    """
    print(f"Attempting to open slide: {wsi_path}")

    # Check if file exists
    if not os.path.exists(wsi_path):
        raise FileNotFoundError(f"File not found: {wsi_path}")

    # Check if OpenSlide can open this file
    if not openslide.is_openslide(wsi_path):
        raise ValueError(f"File is not a valid OpenSlide-compatible whole slide image: {wsi_path}")

    # Load the WSI using OpenSlide
    slide = openslide.OpenSlide(wsi_path)
    print(f"Successfully opened slide with OpenSlide")

    # Get slide properties
    properties = dict(slide.properties)
    print(f"Slide properties: {properties}")

    # Define target patch size
    patch_width, patch_height = 300, 300

    # Get dimensions
    width, height = slide.dimensions
    print(f"Slide dimensions: {width}x{height}")

    # Calculate number of patches
    num_patches_x = max(1, width // patch_width)
    num_patches_y = max(1, height // patch_height)
    print(f"Will extract from grid of {num_patches_x}x{num_patches_y} patches")

    patches_info = []
    patch_count = 0

    # Extract patches in a grid pattern
    max_rows = min(num_patches_y, 5)  # Limit to 5 rows for testing
    max_cols = min(num_patches_x, 5)  # Limit to 5 columns for testing
    print(f"Limited to {max_rows}x{max_cols} grid for testing")

    for y in range(max_rows):
        for x in range(max_cols):
            # Calculate patch coordinates
            x_start = x * patch_width
            y_start = y * patch_height

            try:
                print(f"Extracting patch at ({x_start}, {y_start})")
                # Extract the patch
                patch = slide.read_region((x_start, y_start), 0, (patch_width, patch_height)).convert("RGB")

                # Save the patch
                patch_filename = f"tissue_patch_{patch_count}.png"
                patch_path = os.path.join(output_dir, patch_filename)
                patch.save(patch_path)
                print(f"Saved patch to {patch_path}")

                # Add patch info
                patches_info.append({
                    'id': patch_count,
                    'filename': patch_filename,
                    'x': x_start,
                    'y': y_start,
                    'width': patch_width,
                    'height': patch_height,
                    'patchId': f"patch_{patch_count}.png"  # Add patchId for compatibility
                })

                patch_count += 1

                # Limit to 20 patches for testing
                if patch_count >= 20:
                    print(f"Reached maximum patch count of 20")
                    return patches_info
            except Exception as e:
                print(f"Error extracting patch at ({x_start}, {y_start}): {str(e)}")
                print(traceback.format_exc())
                continue

    print(f"Extracted {patch_count} patches total")
    return patches_info

def extract_patches_with_pil(image_path, output_dir):
    """
    Extract patches using PIL.
    """
    print(f"Attempting to open image with PIL: {image_path}")

    # Check if file exists
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"File not found: {image_path}")

    try:
        # Load the image using PIL
        image = Image.open(image_path)
        print(f"Image format: {image.format}, Size: {image.size}, Mode: {image.mode}")

        # Convert to RGB if needed
        if image.mode != 'RGB':
            print(f"Converting image from {image.mode} to RGB")
            image = image.convert('RGB')

        # Convert to numpy array
        image_np = np.array(image)
        print(f"Numpy array shape: {image_np.shape}")
    except Exception as e:
        print(f"Error opening image with PIL: {str(e)}")
        print(traceback.format_exc())
        raise ValueError(f"Failed to process image with PIL: {str(e)}")

    # Get image dimensions
    if len(image_np.shape) < 2:
        raise Exception("Invalid image dimensions")

    height, width = image_np.shape[:2]
    print(f"Image dimensions: {width}x{height}")

    # Define patch size
    patch_width, patch_height = 300, 300

    # Calculate number of patches
    num_patches_x = max(1, width // patch_width)
    num_patches_y = max(1, height // patch_height)
    print(f"Will extract from grid of {num_patches_x}x{num_patches_y} patches")

    patches_info = []
    patch_count = 0

    # Extract patches in a grid pattern
    max_rows = min(num_patches_y, 5)  # Limit to 5 rows for testing
    max_cols = min(num_patches_x, 5)  # Limit to 5 columns for testing
    print(f"Limited to {max_rows}x{max_cols} grid for testing")

    for y in range(max_rows):
        for x in range(max_cols):
            try:
                # Calculate patch coordinates
                x_start = x * patch_width
                y_start = y * patch_height
                x_end = min(x_start + patch_width, width)
                y_end = min(y_start + patch_height, height)

                print(f"Extracting patch at ({x_start}, {y_start}) to ({x_end}, {y_end})")

                # Extract the patch
                patch_np = image_np[y_start:y_end, x_start:x_end]

                # Skip patches that are too small
                if patch_np.shape[0] < 100 or patch_np.shape[1] < 100:
                    print(f"Skipping small patch: {patch_np.shape}")
                    continue

                # Convert to PIL Image
                patch = Image.fromarray(patch_np)

                # Save the patch
                patch_filename = f"tissue_patch_{patch_count}.png"
                patch_path = os.path.join(output_dir, patch_filename)
                patch.save(patch_path)
                print(f"Saved patch to {patch_path}")

                # Add patch info
                patches_info.append({
                    'id': patch_count,
                    'filename': patch_filename,
                    'x': x_start,
                    'y': y_start,
                    'width': x_end - x_start,
                    'height': y_end - y_start,
                    'patchId': f"patch_{patch_count}.png"  # Add patchId for compatibility
                })

                patch_count += 1

                # Limit to 20 patches for testing
                if patch_count >= 20:
                    print(f"Reached maximum patch count of 20")
                    return patches_info
            except Exception as e:
                print(f"Error extracting patch at ({x_start}, {y_start}): {str(e)}")
                print(traceback.format_exc())
                continue

    print(f"Extracted {patch_count} patches total")
    return patches_info

@app.route('/api/cleanup/<session_id>', methods=['DELETE'])
def cleanup_session(session_id):
    """Clean up session files when analysis is complete"""
    session_folder = os.path.join(PATCHES_FOLDER, session_id)

    if os.path.exists(session_folder):
        shutil.rmtree(session_folder)

    # Also remove the original image file
    for filename in os.listdir(UPLOAD_FOLDER):
        if filename.startswith(f"{session_id}_"):
            os.remove(os.path.join(UPLOAD_FOLDER, filename))

    return jsonify({'success': True})

@app.route('/api/sample-patches', methods=['GET'])
def get_sample_patches():
    try:
        sample_patches_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'sample_patches')
        sample_patches = []
        
        # Read the patches_info.json file
        info_file = os.path.join(sample_patches_dir, 'patches_info.json')
        if os.path.exists(info_file):
            with open(info_file, 'r') as f:
                patches_info = json.load(f)
        else:
            patches_info = {}
        
        # Get all PNG files in the directory
        for filename in os.listdir(sample_patches_dir):
            if filename.endswith('.png'):
                filepath = os.path.join(sample_patches_dir, filename)
                with Image.open(filepath) as img:
                    # Convert image to base64
                    buffered = io.BytesIO()
                    img.save(buffered, format="PNG")
                    img_str = base64.b64encode(buffered.getvalue()).decode()
                    
                    # Get image info from patches_info.json if available
                    info = patches_info.get(filename, {})
                    
                    sample_patches.append({
                        'name': filename,
                        'data': f'data:image/png;base64,{img_str}',
                        'info': info
                    })
        
        return jsonify({'patches': sample_patches})
    except Exception as e:
        print(f"Error getting sample patches: {str(e)}")
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@app.route('/api/upload-chunk', methods=['POST'])
def upload_chunk():
    """Handle chunked file upload"""
    try:
        chunk = request.files['chunk']
        chunk_number = int(request.form['chunkNumber'])
        total_chunks = int(request.form['totalChunks'])
        filename = request.form['filename']
        session_id = request.form.get('sessionId', str(uuid.uuid4()))

        # Create temporary directory for chunks if it doesn't exist
        temp_dir = os.path.join(UPLOAD_FOLDER, f"temp_{session_id}")
        os.makedirs(temp_dir, exist_ok=True)

        # Save the chunk
        chunk_path = os.path.join(temp_dir, f"chunk_{chunk_number}")
        chunk.save(chunk_path)

        # Check if all chunks are received
        received_chunks = len(os.listdir(temp_dir))
        if received_chunks == total_chunks:
            # Combine all chunks
            final_path = os.path.join(UPLOAD_FOLDER, f"{session_id}_{filename}")
            with open(final_path, 'wb') as outfile:
                for i in range(total_chunks):
                    chunk_path = os.path.join(temp_dir, f"chunk_{i}")
                    with open(chunk_path, 'rb') as infile:
                        outfile.write(infile.read())

            # Clean up chunks
            shutil.rmtree(temp_dir)

            # Process the complete file
            return process_uploaded_file(final_path, session_id)
        
        return jsonify({
            'success': True,
            'sessionId': session_id,
            'chunksReceived': received_chunks,
            'totalChunks': total_chunks
        })

    except Exception as e:
        error_traceback = traceback.format_exc()
        print(f"Error handling chunk upload: {str(e)}")
        print(error_traceback)
        return jsonify({'error': str(e), 'traceback': error_traceback}), 500

def process_uploaded_file(file_path, session_id):
    """Process a complete uploaded file"""
    try:
        # Create session folder for patches
        session_folder = os.path.join(PATCHES_FOLDER, session_id)
        os.makedirs(session_folder, exist_ok=True)

        # Check file size
        file_size = os.path.getsize(file_path)
        print(f"File size: {file_size / (1024 * 1024):.2f} MB")

        if file_size > app.config['MAX_CONTENT_LENGTH']:
            raise Exception("File too large. Maximum file size is 1 GB.")

        # Check if it's a TIF file
        if file_path.lower().endswith(('.tif', '.tiff')):
            try:
                print(f"Processing TIF file with OpenSlide: {file_path}")
                patches_info = extract_patches_with_openslide(file_path, session_folder)
                print(f"Successfully extracted {len(patches_info)} patches with OpenSlide")
            except Exception as e:
                print(f"OpenSlide error: {str(e)}")
                print("Falling back to PIL for TIF processing")
                patches_info = extract_patches_with_pil(file_path, session_folder)
        else:
            patches_info = extract_patches_with_pil(file_path, session_folder)

        if not patches_info or len(patches_info) == 0:
            raise Exception("No valid patches could be extracted from the image.")

        return jsonify({
            'success': True,
            'sessionId': session_id,
            'patchesCount': len(patches_info),
            'patches': patches_info
        })

    except Exception as e:
        error_traceback = traceback.format_exc()
        print(f"Error processing file: {str(e)}")
        print(error_traceback)

        # Clean up
        try:
            if os.path.exists(session_folder):
                shutil.rmtree(session_folder)
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception as cleanup_error:
            print(f"Error during cleanup: {str(cleanup_error)}")

        return jsonify({'error': str(e), 'traceback': error_traceback}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080, debug=True)
