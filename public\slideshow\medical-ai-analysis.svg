<svg width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0EA5E9;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:0.2" />
    </linearGradient>
    <linearGradient id="aiGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0EA5E9" />
      <stop offset="100%" style="stop-color:#8B5CF6" />
    </linearGradient>
    <linearGradient id="cellGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981" />
      <stop offset="100%" style="stop-color:#06B6D4" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="400" fill="url(#bgGradient1)"/>
  
  <!-- Medical Grid Pattern -->
  <pattern id="medicalGrid" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
    <circle cx="20" cy="20" r="1" fill="#0EA5E9" opacity="0.1"/>
    <path d="M18 20h4M20 18v4" stroke="#0EA5E9" stroke-width="0.5" opacity="0.1"/>
  </pattern>
  <rect width="600" height="400" fill="url(#medicalGrid)"/>
  
  <!-- AI Brain/Neural Network -->
  <g transform="translate(50, 80)">
    <!-- Central AI Core -->
    <circle cx="100" cy="80" r="40" fill="url(#aiGradient)" opacity="0.8"/>
    <circle cx="100" cy="80" r="30" fill="none" stroke="#ffffff" stroke-width="2" opacity="0.6"/>
    <circle cx="100" cy="80" r="20" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
    
    <!-- Neural Connections -->
    <g stroke="url(#aiGradient)" stroke-width="2" fill="none" opacity="0.6">
      <path d="M70 60 Q85 45 120 50"/>
      <path d="M70 100 Q85 115 120 110"/>
      <path d="M130 80 Q145 65 170 70"/>
      <path d="M130 80 Q145 95 170 90"/>
      <path d="M60 80 Q45 65 20 70"/>
      <path d="M60 80 Q45 95 20 90"/>
    </g>
    
    <!-- Neural Nodes -->
    <circle cx="20" cy="70" r="6" fill="#10B981"/>
    <circle cx="20" cy="90" r="6" fill="#10B981"/>
    <circle cx="170" cy="70" r="6" fill="#10B981"/>
    <circle cx="170" cy="90" r="6" fill="#10B981"/>
    <circle cx="120" cy="50" r="6" fill="#10B981"/>
    <circle cx="120" cy="110" r="6" fill="#10B981"/>
    
    <!-- AI Text -->
    <text x="100" y="85" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">AI</text>
  </g>
  
  <!-- Tissue Cells -->
  <g transform="translate(300, 60)">
    <!-- Cell 1 -->
    <circle cx="50" cy="50" r="35" fill="url(#cellGradient)" opacity="0.7"/>
    <circle cx="50" cy="50" r="25" fill="none" stroke="#ffffff" stroke-width="1.5" opacity="0.8"/>
    <circle cx="45" cy="45" r="8" fill="#ffffff" opacity="0.6"/>
    <circle cx="55" cy="55" r="5" fill="#ffffff" opacity="0.4"/>
    
    <!-- Cell 2 -->
    <circle cx="120" cy="80" r="30" fill="url(#cellGradient)" opacity="0.6"/>
    <circle cx="120" cy="80" r="20" fill="none" stroke="#ffffff" stroke-width="1.5" opacity="0.8"/>
    <circle cx="115" cy="75" r="6" fill="#ffffff" opacity="0.6"/>
    <circle cx="125" cy="85" r="4" fill="#ffffff" opacity="0.4"/>
    
    <!-- Cell 3 -->
    <circle cx="180" cy="40" r="25" fill="url(#cellGradient)" opacity="0.8"/>
    <circle cx="180" cy="40" r="18" fill="none" stroke="#ffffff" stroke-width="1.5" opacity="0.8"/>
    <circle cx="175" cy="35" r="5" fill="#ffffff" opacity="0.6"/>
  </g>
  
  <!-- Analysis Arrows -->
  <g stroke="#0EA5E9" stroke-width="3" fill="none" opacity="0.7">
    <path d="M200 140 Q250 120 300 140" marker-end="url(#arrowhead)"/>
    <path d="M200 160 Q250 180 300 160" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- Arrow Marker -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#0EA5E9"/>
    </marker>
  </defs>
  
  <!-- Results Display -->
  <g transform="translate(400, 250)">
    <rect x="0" y="0" width="180" height="120" rx="10" fill="rgba(255,255,255,0.9)" stroke="#0EA5E9" stroke-width="2"/>
    <text x="90" y="25" text-anchor="middle" fill="#1e3a8a" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Analysis Results</text>
    
    <!-- Progress Bars -->
    <g transform="translate(20, 40)">
      <text x="0" y="15" fill="#64748B" font-family="Arial, sans-serif" font-size="10">Stroma: 65%</text>
      <rect x="0" y="20" width="140" height="8" rx="4" fill="#E2E8F0"/>
      <rect x="0" y="20" width="91" height="8" rx="4" fill="#10B981"/>
      
      <text x="0" y="40" fill="#64748B" font-family="Arial, sans-serif" font-size="10">Tumor: 25%</text>
      <rect x="0" y="45" width="140" height="8" rx="4" fill="#E2E8F0"/>
      <rect x="0" y="45" width="35" height="8" rx="4" fill="#F59E0B"/>
      
      <text x="0" y="65" fill="#64748B" font-family="Arial, sans-serif" font-size="10">Vessel: 10%</text>
      <rect x="0" y="70" width="140" height="8" rx="4" fill="#E2E8F0"/>
      <rect x="0" y="70" width="14" height="8" rx="4" fill="#8B5CF6"/>
    </g>
  </g>
  
  <!-- Floating Medical Icons -->
  <g opacity="0.3">
    <!-- Microscope -->
    <g transform="translate(480, 50)">
      <circle cx="15" cy="15" r="8" fill="none" stroke="#0EA5E9" stroke-width="2"/>
      <rect x="12" y="23" width="6" height="15" fill="#0EA5E9"/>
      <rect x="8" y="38" width="14" height="4" fill="#0EA5E9"/>
    </g>
    
    <!-- DNA Helix -->
    <g transform="translate(30, 300)">
      <path d="M0 0 Q10 10 20 0 Q30 -10 40 0" stroke="#10B981" stroke-width="2" fill="none"/>
      <path d="M0 20 Q10 10 20 20 Q30 30 40 20" stroke="#10B981" stroke-width="2" fill="none"/>
      <circle cx="10" cy="5" r="2" fill="#10B981"/>
      <circle cx="30" cy="5" r="2" fill="#10B981"/>
      <circle cx="10" cy="15" r="2" fill="#10B981"/>
      <circle cx="30" cy="15" r="2" fill="#10B981"/>
    </g>
  </g>
</svg>
