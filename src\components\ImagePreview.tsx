
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AspectRatio } from '@/components/ui/aspect-ratio';

interface ImagePreviewProps {
  files: File[];
  selectedIndex: number;
  onSelectImage: (index: number) => void;
}

const ImagePreview: React.FC<ImagePreviewProps> = ({ 
  files, 
  selectedIndex,
  onSelectImage
}) => {
  const [imageUrls, setImageUrls] = useState<string[]>([]);

  // Generate URLs for the image previews
  React.useEffect(() => {
    const urls = files.map(file => URL.createObjectURL(file));
    setImageUrls(urls);
    
    // Cleanup function to revoke object URLs
    return () => {
      urls.forEach(url => URL.revokeObjectURL(url));
    };
  }, [files]);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg flex justify-between items-center">
          <span>Patch Samples ({files.length})</span>
          <Badge variant="outline" className="font-normal">
            {selectedIndex + 1} of {files.length}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {files.length > 0 && (
          <div className="space-y-4">
            {/* Selected image preview */}
            <div className="border rounded-md overflow-hidden">
              <AspectRatio ratio={16/9}>
                {imageUrls[selectedIndex] && (
                  <img 
                    src={imageUrls[selectedIndex]} 
                    alt={`Patch ${selectedIndex + 1}`}
                    className="w-full h-full object-cover"
                  />
                )}
              </AspectRatio>
            </div>
            
            {/* Thumbnail strip */}
            <div className="flex space-x-2 overflow-x-auto py-2">
              {imageUrls.map((url, index) => (
                <div 
                  key={index}
                  onClick={() => onSelectImage(index)}
                  className={`cursor-pointer flex-shrink-0 w-16 h-16 border-2 rounded overflow-hidden 
                    ${selectedIndex === index ? 'border-blue-500' : 'border-transparent'}`}
                >
                  <img 
                    src={url} 
                    alt={`Thumbnail ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ImagePreview;
