<svg width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient4" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F59E0B;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:0.2" />
    </linearGradient>
    <linearGradient id="globeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0EA5E9" />
      <stop offset="100%" style="stop-color:#1e3a8a" />
    </linearGradient>
    <linearGradient id="impactGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#F59E0B" />
      <stop offset="50%" style="stop-color:#10B981" />
      <stop offset="100%" style="stop-color:#0EA5E9" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="400" fill="url(#bgGradient4)"/>
  
  <!-- Global Pattern -->
  <pattern id="globalPattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
    <circle cx="50" cy="50" r="3" fill="#F59E0B" opacity="0.1"/>
    <circle cx="25" cy="25" r="2" fill="#10B981" opacity="0.1"/>
    <circle cx="75" cy="75" r="2" fill="#0EA5E9" opacity="0.1"/>
    <circle cx="25" cy="75" r="1" fill="#8B5CF6" opacity="0.1"/>
    <circle cx="75" cy="25" r="1" fill="#8B5CF6" opacity="0.1"/>
  </pattern>
  <rect width="600" height="400" fill="url(#globalPattern)"/>
  
  <!-- World Globe -->
  <g transform="translate(250, 150)">
    <!-- Globe Base -->
    <circle cx="50" cy="50" r="45" fill="url(#globeGradient)" opacity="0.8"/>
    <circle cx="50" cy="50" r="40" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.6"/>
    
    <!-- Continents (simplified) -->
    <g fill="#ffffff" opacity="0.7">
      <!-- North America -->
      <path d="M25 30 Q35 25 40 35 Q35 40 30 35 Q25 38 25 30"/>
      <!-- Europe -->
      <path d="M45 25 Q50 20 55 25 Q50 30 45 25"/>
      <!-- Asia -->
      <path d="M55 25 Q70 20 75 35 Q65 40 55 35 Q55 30 55 25"/>
      <!-- Africa -->
      <path d="M45 35 Q50 30 55 40 Q50 50 45 45 Q40 40 45 35"/>
      <!-- South America -->
      <path d="M30 50 Q35 45 40 55 Q35 65 30 60 Q25 55 30 50"/>
      <!-- Australia -->
      <path d="M65 60 Q70 55 75 60 Q70 65 65 60"/>
    </g>
    
    <!-- Globe Grid Lines -->
    <g stroke="#ffffff" stroke-width="0.5" fill="none" opacity="0.3">
      <!-- Latitude lines -->
      <ellipse cx="50" cy="50" rx="40" ry="15"/>
      <ellipse cx="50" cy="50" rx="40" ry="30"/>
      <!-- Longitude lines -->
      <ellipse cx="50" cy="50" rx="15" ry="40"/>
      <ellipse cx="50" cy="50" rx="30" ry="40"/>
    </g>
    
    <!-- Globe Highlight -->
    <circle cx="40" cy="40" r="8" fill="#ffffff" opacity="0.3"/>
  </g>
  
  <!-- Impact Statistics -->
  <g transform="translate(50, 80)">
    <!-- Stat 1 -->
    <g>
      <circle cx="30" cy="30" r="25" fill="rgba(245, 158, 11, 0.2)" stroke="#F59E0B" stroke-width="2"/>
      <text x="30" y="25" text-anchor="middle" fill="#F59E0B" font-family="Arial, sans-serif" font-size="12" font-weight="bold">10K+</text>
      <text x="30" y="38" text-anchor="middle" fill="#F59E0B" font-family="Arial, sans-serif" font-size="8">Lives Saved</text>
      <text x="30" y="65" text-anchor="middle" fill="#F59E0B" font-family="Arial, sans-serif" font-size="9" font-weight="bold">Lives Impacted</text>
    </g>
    
    <!-- Stat 2 -->
    <g transform="translate(0, 120)">
      <circle cx="30" cy="30" r="25" fill="rgba(16, 185, 129, 0.2)" stroke="#10B981" stroke-width="2"/>
      <text x="30" y="25" text-anchor="middle" fill="#10B981" font-family="Arial, sans-serif" font-size="12" font-weight="bold">25+</text>
      <text x="30" y="38" text-anchor="middle" fill="#10B981" font-family="Arial, sans-serif" font-size="8">Countries</text>
      <text x="30" y="65" text-anchor="middle" fill="#10B981" font-family="Arial, sans-serif" font-size="9" font-weight="bold">Global Reach</text>
    </g>
  </g>
  
  <!-- Impact Connections -->
  <g transform="translate(400, 80)">
    <!-- Stat 3 -->
    <g>
      <circle cx="30" cy="30" r="25" fill="rgba(14, 165, 233, 0.2)" stroke="#0EA5E9" stroke-width="2"/>
      <text x="30" y="25" text-anchor="middle" fill="#0EA5E9" font-family="Arial, sans-serif" font-size="12" font-weight="bold">98%</text>
      <text x="30" y="38" text-anchor="middle" fill="#0EA5E9" font-family="Arial, sans-serif" font-size="8">Success Rate</text>
      <text x="30" y="65" text-anchor="middle" fill="#0EA5E9" font-family="Arial, sans-serif" font-size="9" font-weight="bold">Clinical Success</text>
    </g>
    
    <!-- Stat 4 -->
    <g transform="translate(0, 120)">
      <circle cx="30" cy="30" r="25" fill="rgba(139, 92, 246, 0.2)" stroke="#8B5CF6" stroke-width="2"/>
      <text x="30" y="25" text-anchor="middle" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="12" font-weight="bold">24/7</text>
      <text x="30" y="38" text-anchor="middle" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="8">Available</text>
      <text x="30" y="65" text-anchor="middle" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="9" font-weight="bold">Always Ready</text>
    </g>
  </g>
  
  <!-- Connection Lines -->
  <g stroke="url(#impactGradient)" stroke-width="2" fill="none" opacity="0.5">
    <!-- From left stats to globe -->
    <path d="M105 110 Q180 100 250 150"/>
    <path d="M105 230 Q180 220 250 200"/>
    
    <!-- From globe to right stats -->
    <path d="M350 150 Q380 100 400 110"/>
    <path d="M350 200 Q380 220 400 230"/>
  </g>
  
  <!-- Medical Cross Icons -->
  <g opacity="0.4">
    <!-- Cross 1 -->
    <g transform="translate(150, 50)">
      <path d="M10 5 L10 15 M5 10 L15 10" stroke="#10B981" stroke-width="2"/>
      <circle cx="10" cy="10" r="8" fill="none" stroke="#10B981" stroke-width="1"/>
    </g>
    
    <!-- Cross 2 -->
    <g transform="translate(450, 280)">
      <path d="M10 5 L10 15 M5 10 L15 10" stroke="#0EA5E9" stroke-width="2"/>
      <circle cx="10" cy="10" r="8" fill="none" stroke="#0EA5E9" stroke-width="1"/>
    </g>
    
    <!-- Cross 3 -->
    <g transform="translate(100, 300)">
      <path d="M10 5 L10 15 M5 10 L15 10" stroke="#8B5CF6" stroke-width="2"/>
      <circle cx="10" cy="10" r="8" fill="none" stroke="#8B5CF6" stroke-width="1"/>
    </g>
  </g>
  
  <!-- Impact Message -->
  <g transform="translate(150, 320)">
    <rect x="0" y="0" width="300" height="60" rx="15" fill="rgba(255,255,255,0.95)" stroke="url(#impactGradient)" stroke-width="2"/>
    <text x="150" y="25" text-anchor="middle" fill="#1e3a8a" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Making a Global Difference</text>
    <text x="150" y="40" text-anchor="middle" fill="#64748B" font-family="Arial, sans-serif" font-size="10">Advanced AI technology improving women's health worldwide</text>
    <text x="150" y="52" text-anchor="middle" fill="#64748B" font-family="Arial, sans-serif" font-size="10">through early detection and accessible screening</text>
  </g>
  
  <!-- Pulse Animation Circles -->
  <g opacity="0.3">
    <circle cx="300" cy="200" r="80" fill="none" stroke="#F59E0B" stroke-width="1">
      <animate attributeName="r" values="80;120;80" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.3;0;0.3" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="300" cy="200" r="100" fill="none" stroke="#10B981" stroke-width="1">
      <animate attributeName="r" values="100;140;100" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.3;0;0.3" dur="4s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg>
