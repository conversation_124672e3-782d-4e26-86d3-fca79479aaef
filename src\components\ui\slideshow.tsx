import React, { useState, useEffect, useCallback } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from "@/lib/utils";

export interface Slide {
  image?: string;
  title?: string;
  description?: string;
  content?: React.ReactNode;
  [key: string]: any; // Allow additional properties
}

interface SlideshowProps {
  slides: Slide[];
  autoPlay?: boolean;
  interval?: number;
  className?: string;
  showArrows?: boolean;
  showDots?: boolean;
  renderSlide?: (slide: Slide) => React.ReactNode;
}

export const Slideshow: React.FC<SlideshowProps> = ({
  slides,
  autoPlay = true,
  interval = 5000,
  className,
  showArrows = true,
  showDots = true,
  renderSlide,
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  const nextSlide = useCallback(() => {
    setCurrentSlide((current) => (current + 1) % slides.length);
  }, [slides.length]);

  const previousSlide = useCallback(() => {
    setCurrentSlide((current) => (current === 0 ? slides.length - 1 : current - 1));
  }, [slides.length]);

  useEffect(() => {
    if (autoPlay && !isHovered) {
      const timer = setInterval(nextSlide, interval);
      return () => clearInterval(timer);
    }
  }, [autoPlay, interval, nextSlide, isHovered]);

  return (
    <div
      className={cn(
        "relative w-full overflow-hidden rounded-xl",
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div
        className="flex transition-transform duration-500 ease-out"
        style={{
          transform: `translateX(-${currentSlide * 100}%)`,
          width: `${slides.length * 100}%`,
        }}
      >
        {slides.map((slide, index) => (
          <div
            key={index}
            className="relative w-full h-full"
            style={{ width: `${100 / slides.length}%` }}
          >
            {renderSlide ? (
              renderSlide(slide)
            ) : (
              <>
                {slide.image && (
                  <img
                    src={slide.image}
                    alt={slide.title || `Slide ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                )}
                {(slide.title || slide.description) && (
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-6 text-white">
                    {slide.title && (
                      <h3 className="text-xl font-semibold mb-2">{slide.title}</h3>
                    )}
                    {slide.description && (
                      <p className="text-sm text-white/90">{slide.description}</p>
                    )}
                  </div>
                )}
                {slide.content && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    {slide.content}
                  </div>
                )}
              </>
            )}
          </div>
        ))}
      </div>

      {showArrows && (
        <>
          <button
            onClick={previousSlide}
            className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-primary-navy rounded-full p-2 transition-colors duration-200"
            aria-label="Previous slide"
          >
            <ChevronLeft className="h-6 w-6" />
          </button>
          <button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-primary-navy rounded-full p-2 transition-colors duration-200"
            aria-label="Next slide"
          >
            <ChevronRight className="h-6 w-6" />
          </button>
        </>
      )}

      {showDots && (
        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
          {slides.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={cn(
                "w-2 h-2 rounded-full transition-all duration-200",
                currentSlide === index
                  ? "bg-white w-4"
                  : "bg-white/50 hover:bg-white/75"
              )}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
};
