/* Medical Theme Enhanced CSS */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700;800&display=swap');

/* Custom CSS Variables for Medical Theme */
:root {
  --primary-navy: #1e3a8a;
  --cerulean: #0EA5E9;
  --sky-blue: #7DD3FC;
  --medical-mint: #10B981;
  --lavender: #8B5CF6;
  --medium-gray: #64748B;
  --light-gray: #F8FAFC;

  /* Medical gradients */
  --gradient-medical-primary: linear-gradient(135deg, #0EA5E9 0%, #1e3a8a 100%);
  --gradient-medical-secondary: linear-gradient(135deg, #10B981 0%, #06B6D4 100%);
  --gradient-medical-accent: linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%);
  --gradient-hero-bg: linear-gradient(135deg, #F0F9FF 0%, #EFF6FF 50%, #F5F3FF 100%);

  /* Medical shadows */
  --shadow-medical-card: 0 4px 6px -1px rgba(14, 165, 233, 0.1), 0 2px 4px -1px rgba(14, 165, 233, 0.06);
  --shadow-medical-hover: 0 10px 15px -3px rgba(14, 165, 233, 0.1), 0 4px 6px -2px rgba(14, 165, 233, 0.05);
  --shadow-medical-glow: 0 0 20px rgba(14, 165, 233, 0.15);
}

/* Reset and base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: var(--medium-gray);
  background: var(--gradient-hero-bg);
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

#root {
  min-height: 100vh;
  width: 100%;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  line-height: 1.2;
  color: var(--primary-navy);
}

/* Medical pattern backgrounds */
.medical-pattern {
  background-image:
    radial-gradient(circle at 25% 25%, rgba(14, 165, 233, 0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.1) 2px, transparent 2px);
  background-size: 50px 50px;
  background-position: 0 0, 25px 25px;
}

.medical-grid {
  background-image:
    linear-gradient(rgba(14, 165, 233, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(14, 165, 233, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulseSoft {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Utility classes */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.8s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out;
}

.animate-pulse-soft {
  animation: pulseSoft 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Medical card styles */
.medical-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(14, 165, 233, 0.1);
  box-shadow: var(--shadow-medical-card);
  transition: all 0.3s ease;
}

.medical-card:hover {
  box-shadow: var(--shadow-medical-hover);
  transform: translateY(-2px);
}

/* Gradient text */
.gradient-text {
  background: var(--gradient-medical-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Medical button styles */
.btn-medical {
  background: var(--gradient-medical-primary);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-medical-card);
}

.btn-medical:hover {
  box-shadow: var(--shadow-medical-hover);
  transform: translateY(-1px);
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--light-gray);
}

::-webkit-scrollbar-thumb {
  background: var(--cerulean);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-navy);
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid var(--cerulean);
  outline-offset: 2px;
}

/* Loading spinner */
.spinner {
  border: 3px solid rgba(14, 165, 233, 0.1);
  border-top: 3px solid var(--cerulean);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

/* Container styles */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container {
    padding: 0 2rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 3rem;
  }
}

/* Medical themed components */
.medical-hero {
  background: var(--gradient-hero-bg);
  position: relative;
  overflow: hidden;
}

.medical-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('/medical-backgrounds/medical-pattern.svg');
  background-size: 200px 200px;
  background-repeat: repeat;
  opacity: 0.05;
  z-index: 1;
}

.medical-hero > * {
  position: relative;
  z-index: 2;
}

/* Chart and visualization enhancements */
.chart-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: var(--shadow-medical-card);
}

/* Progress bar styling */
.progress-medical {
  background: rgba(14, 165, 233, 0.1);
  border-radius: 9999px;
  overflow: hidden;
}

.progress-medical-fill {
  background: var(--gradient-medical-primary);
  height: 100%;
  border-radius: 9999px;
  transition: width 0.5s ease;
}

/* Badge styling */
.badge-medical {
  background: var(--gradient-medical-primary);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-medical-hover);
}

/* Text effects */
.text-glow {
  text-shadow: 0 0 10px rgba(14, 165, 233, 0.3);
}

/* Responsive utilities */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }

  .mobile-full {
    width: 100%;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none;
  }

  body {
    background: white;
    color: black;
  }
}
