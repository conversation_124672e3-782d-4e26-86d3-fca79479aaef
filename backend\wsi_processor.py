from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import uuid
import numpy as np
import cv2
import openslide
from PIL import Image
import time
import shutil
import traceback
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans

app = Flask(__name__)
# Configure Flask for larger file uploads
app.config['MAX_CONTENT_LENGTH'] = 1000 * 1024 * 1024  # 1000 MB max upload

# Configure CORS to allow all origins, methods, and headers
CORS(app, resources={r"/*": {
    "origins": "*",
    "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    "allow_headers": ["Content-Type", "Authorization", "X-Requested-With"]
}})

# Configure upload folder
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
PATCHES_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'patches')

# Create directories if they don't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(PATCHES_FOLDER, exist_ok=True)

@app.route('/api/test', methods=['GET', 'OPTIONS'])
def test_endpoint():
    """Simple test endpoint to verify the server is accessible"""
    print("\n" + "="*50)
    print("Test endpoint accessed")
    print(f"Request method: {request.method}")
    print(f"Request headers: {dict(request.headers)}")
    print("="*50 + "\n")

    # Add CORS headers manually to ensure they're present
    response = jsonify({
        'status': 'ok',
        'message': 'Server is running',
        'timestamp': time.time(),
        'cors_enabled': True
    })

    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type')

    return response

@app.route('/', methods=['GET'])
def root_endpoint():
    """Root endpoint for basic connectivity testing"""
    return jsonify({'status': 'ok', 'message': 'WSI Processor API is running'})

@app.route('/api/upload-wsi', methods=['POST'])
def upload_wsi():
    print("\n" + "="*50)
    print("Received upload request")
    print(f"Request method: {request.method}")
    print(f"Request path: {request.path}")
    print(f"Request headers: {dict(request.headers)}")
    print("="*50 + "\n")

    if 'file' not in request.files:
        print("No file part in request")
        return jsonify({'error': 'No file part in request'}), 400

    file = request.files['file']

    if file.filename == '':
        print("No selected file")
        return jsonify({'error': 'No selected file'}), 400

    print(f"Processing file: {file.filename}")
    print(f"File content type: {file.content_type}")

    # Generate a unique ID for this upload
    session_id = str(uuid.uuid4())
    session_folder = os.path.join(PATCHES_FOLDER, session_id)
    os.makedirs(session_folder, exist_ok=True)

    # Save the uploaded file
    file_path = os.path.join(UPLOAD_FOLDER, f"{session_id}_{file.filename}")
    file.save(file_path)
    print(f"Saved file to {file_path}")
    print(f"File size: {os.path.getsize(file_path)} bytes")

    try:
        # Check if the file exists and is readable
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Saved file not found at {file_path}")

        if not os.access(file_path, os.R_OK):
            raise PermissionError(f"Cannot read file at {file_path}")

        # Process the WSI and extract patches
        print("Starting patch extraction...")
        patches_info = extract_patches_from_wsi(file_path, session_folder)
        print(f"Patch extraction complete. Extracted {len(patches_info)} patches.")

        response_data = {
            'success': True,
            'sessionId': session_id,
            'patchesCount': len(patches_info),
            'patches': patches_info
        }

        return jsonify(response_data)
    except Exception as e:
        error_traceback = traceback.format_exc()
        print(f"Error processing image: {str(e)}")
        print(error_traceback)

        # Clean up any files that might have been created
        try:
            if os.path.exists(session_folder):
                print(f"Cleaning up session folder: {session_folder}")
                shutil.rmtree(session_folder)
            if os.path.exists(file_path):
                print(f"Cleaning up uploaded file: {file_path}")
                os.remove(file_path)
        except Exception as cleanup_error:
            print(f"Error during cleanup: {str(cleanup_error)}")

        return jsonify({
            'error': str(e),
            'traceback': error_traceback,
            'message': 'Failed to process whole slide image. See server logs for details.'
        }), 500

# === Reinhard Color Normalization ===
def reinhard_normalization(image, target_mean=None, target_std=None):
    """
    Applies Reinhard color normalization to standardize appearance.
    Uses LAB color space for normalization.
    """
    if target_mean is None:
        target_mean = [128, 128, 128]  # Default target means for LAB
    if target_std is None:
        target_std = [64, 64, 64]  # Default target stds for LAB

    # Convert to LAB color space
    lab_image = cv2.cvtColor(image, cv2.COLOR_RGB2LAB)
    lab_image = lab_image.astype(np.float32)

    # Calculate mean and std for each channel
    mean, std = cv2.meanStdDev(lab_image)
    mean = mean.flatten()
    std = std.flatten()

    # Normalize each channel
    for i in range(3):
        lab_image[:,:,i] = (lab_image[:,:,i] - mean[i]) * (target_std[i] / std[i]) + target_mean[i]

    # Clip values to valid range and convert back to RGB
    lab_image = np.clip(lab_image, 0, 255)
    normalized_image = cv2.cvtColor(lab_image.astype(np.uint8), cv2.COLOR_LAB2RGB)

    return normalized_image

# === Optimized Tissue Detection ===
def optimized_tissue_detection(image):
    """
    Efficient tissue detection using adaptive thresholding and morphological operations.
    Returns a binary mask where tissue regions are white.
    """
    # Convert to HSV and use saturation channel (better for tissue detection)
    hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
    saturation = hsv[:,:,1]

    # Adaptive thresholding
    mask = cv2.adaptiveThreshold(saturation, 255, cv2.ADAPTIVE_THRESH_MEAN_C,
                                cv2.THRESH_BINARY, 51, 5)

    # Morphological operations to clean up the mask
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5,5))
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=1)
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=1)

    # Remove small artifacts
    mask = cv2.erode(mask, kernel, iterations=1)

    return mask

# === Non-overlapping Patch Extraction ===
def extract_non_overlapping_patches(slide, contours, thumbnail_size, patch_size, output_dir):
    """
    Extracts non-overlapping patches that cover tissue regions efficiently.
    Uses grid-based approach with tissue presence verification.
    """
    # Calculate scaling factors
    scale_x = slide.dimensions[0] / thumbnail_size[0]
    scale_y = slide.dimensions[1] / thumbnail_size[1]

    # Create a grid covering the entire WSI
    grid_x = int(slide.dimensions[0] / patch_size[0])
    grid_y = int(slide.dimensions[1] / patch_size[1])

    # Create a coverage mask to track extracted regions
    coverage_mask = np.zeros((grid_y, grid_x), dtype=bool)

    # Process each contour to mark tissue-containing grid cells
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)

        # Convert to WSI coordinates
        abs_x = int(x * scale_x)
        abs_y = int(y * scale_y)
        abs_w = int(w * scale_x)
        abs_h = int(h * scale_y)

        # Calculate grid cells that overlap with this contour
        start_x = max(0, abs_x // patch_size[0])
        end_x = min(grid_x - 1, (abs_x + abs_w) // patch_size[0])
        start_y = max(0, abs_y // patch_size[1])
        end_y = min(grid_y - 1, (abs_y + abs_h) // patch_size[1])

        # Mark these grid cells as containing tissue
        coverage_mask[start_y:end_y+1, start_x:end_x+1] = True

    # Extract patches from marked grid cells
    patch_count = 0
    tissue_patches = []
    patches_info = []

    for y in range(grid_y):
        for x in range(grid_x):
            if coverage_mask[y, x]:
                # Calculate WSI coordinates for this patch
                x_patch = x * patch_size[0]
                y_patch = y * patch_size[1]

                # Ensure we don't go out of bounds
                if x_patch + patch_size[0] > slide.dimensions[0]:
                    x_patch = slide.dimensions[0] - patch_size[0]
                if y_patch + patch_size[1] > slide.dimensions[1]:
                    y_patch = slide.dimensions[1] - patch_size[1]

                try:
                    # Extract the patch
                    patch = slide.read_region((x_patch, y_patch), 0, patch_size).convert("RGB")
                    patch_np = np.array(patch)

                    # Verify tissue content (exclude mostly white patches)
                    if np.mean(patch_np) < 230:  # Simple intensity check
                        tissue_patches.append(patch_np)
                        patch_filename = f"tissue_patch_{patch_count}.png"
                        patch_path = os.path.join(output_dir, patch_filename)

                        # Ensure the directory exists right before saving
                        os.makedirs(os.path.dirname(patch_path), exist_ok=True)

                        patch.save(patch_path)
                        print(f"Saved patch {patch_count} to {patch_path}")

                        # Add patch info for API response
                        patches_info.append({
                            'id': patch_count,
                            'filename': patch_filename,
                            'x': x_patch,
                            'y': y_patch,
                            'width': patch_size[0],
                            'height': patch_size[1],
                            'patchId': f"patch_{patch_count}.png"  # Add patchId for compatibility
                        })

                        patch_count += 1

                        # Limit to 20 patches for web display
                        if patch_count >= 20:
                            print(f"Reached limit of 20 patches for web display")
                            return patches_info
                except Exception as e:
                    print(f"Error processing patch at ({x_patch}, {y_patch}): {str(e)}")
                    continue

    print(f"Extracted {patch_count} patches total")
    return patches_info

def extract_patches_from_wsi(wsi_path, output_dir):
    """
    Extract patches from a whole slide image using the provided code.
    Returns information about the extracted patches.
    """
    print(f"Processing WSI: {wsi_path}")

    # Check if the file exists
    if not os.path.exists(wsi_path):
        raise FileNotFoundError(f"WSI file not found: {wsi_path}")

    # Check file size
    file_size_mb = os.path.getsize(wsi_path) / (1024 * 1024)
    print(f"WSI file size: {file_size_mb:.2f} MB")

    try:
        # Load the WSI using OpenSlide
        print(f"Opening slide with OpenSlide: {wsi_path}")
        slide = openslide.OpenSlide(wsi_path)
        print(f"Slide dimensions: {slide.dimensions}")

        # Define target patch size - using the exact values from the provided code
        target_width, target_height = 1080, 566  # Target size for output patches
        patch_size = (target_width, target_height)

        # Read a downsampled thumbnail for quick processing
        print("Generating thumbnail...")
        thumbnail_size = (2048, 2048)  # Smaller thumbnail for faster processing
        thumbnail = slide.get_thumbnail(thumbnail_size)
        thumbnail = thumbnail.convert("RGB")
        print(f"Thumbnail size: {thumbnail.size}")

        # Save thumbnail for reference
        thumbnail_path = os.path.join(output_dir, "thumbnail.png")
        thumbnail.save(thumbnail_path)
        print(f"Saved thumbnail to {thumbnail_path}")

        # Convert thumbnail to NumPy array for processing
        wsi_image_np = np.array(thumbnail)

        # Apply Reinhard normalization
        print("Applying Reinhard normalization...")
        normalized_wsi = reinhard_normalization(wsi_image_np)

        # Save normalized image for reference
        normalized_path = os.path.join(output_dir, "normalized.png")
        Image.fromarray(normalized_wsi).save(normalized_path)
        print(f"Saved normalized image to {normalized_path}")

        # Get tissue mask
        print("Generating tissue mask...")
        tissue_mask = optimized_tissue_detection(normalized_wsi)

        # Save tissue mask for reference
        mask_path = os.path.join(output_dir, "tissue_mask.png")
        Image.fromarray(tissue_mask).save(mask_path)
        print(f"Saved tissue mask to {mask_path}")

        # Find contours of tissue regions
        print("Finding tissue contours...")
        contours, _ = cv2.findContours(tissue_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        print(f"Found {len(contours)} tissue regions")

        # Extract non-overlapping patches
        start_time = time.time()
        patches_info = extract_non_overlapping_patches(
            slide, contours, thumbnail_size, patch_size, output_dir
        )
        end_time = time.time()

        print(f"Patch extraction time: {end_time - start_time:.2f} seconds")

        return patches_info
    except Exception as e:
        print(f"Error in extract_patches_from_wsi: {str(e)}")
        print(traceback.format_exc())
        raise

@app.route('/api/patches/<session_id>/<patch_filename>', methods=['GET'])
def get_patch(session_id, patch_filename):
    patch_folder = os.path.join(PATCHES_FOLDER, session_id)
    print(f"Requested patch: {patch_filename} from session {session_id}")
    print(f"Looking in folder: {patch_folder}")

    # Check if the file exists
    file_path = os.path.join(patch_folder, patch_filename)
    if os.path.exists(file_path):
        print(f"File found: {file_path}")
        return send_from_directory(patch_folder, patch_filename)
    else:
        print(f"File not found: {file_path}")
        return jsonify({'error': 'Patch not found'}), 404

@app.route('/api/cleanup/<session_id>', methods=['DELETE'])
def cleanup_session(session_id):
    """Clean up session files when analysis is complete"""
    session_folder = os.path.join(PATCHES_FOLDER, session_id)
    print(f"Cleaning up session: {session_id}")

    if os.path.exists(session_folder):
        print(f"Removing session folder: {session_folder}")
        shutil.rmtree(session_folder)
    else:
        print(f"Session folder not found: {session_folder}")

    # Also remove the original image file
    for filename in os.listdir(UPLOAD_FOLDER):
        if filename.startswith(f"{session_id}_"):
            file_path = os.path.join(UPLOAD_FOLDER, filename)
            print(f"Removing uploaded file: {file_path}")
            os.remove(file_path)

    return jsonify({'success': True})

if __name__ == '__main__':
    # Enable more verbose logging
    import logging
    logging.basicConfig(level=logging.DEBUG)

    # Print available sample patches
    sample_patches_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'sample_patches')
    if os.path.exists(sample_patches_dir):
        print(f"Available sample patches: {os.listdir(sample_patches_dir)}")
    else:
        print("Sample patches directory not found")

    app.run(host='0.0.0.0', port=8080, debug=True)
