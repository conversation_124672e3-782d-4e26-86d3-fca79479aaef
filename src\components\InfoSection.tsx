import React from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { LineChart, Activity, Users, AlertCircle, Book, Microscope, HeartPulse } from 'lucide-react';

const InfoSection = () => {
  const stats = [
    {
      title: 'Global Cases',
      value: '420,000+',
      description: 'New cases reported annually',
      color: 'bg-gradient-to-br from-sky-500 to-sky-600',
      icon: LineChart,
    },
    {
      title: 'Early Detection',
      value: '96%',
      description: '5-year survival rate when detected early',
      color: 'bg-gradient-to-br from-teal-500 to-teal-600',
      icon: Activity,
    },
    {
      title: 'Ranking',
      value: '6th',
      description: 'Most common cancer among women',
      color: 'bg-gradient-to-br from-indigo-500 to-indigo-600',
      icon: Users,
    },
  ];

  const tabContent = {
    about: {
      title: 'What is Endometrial Cancer?',
      content: [
        'Endometrial cancer begins in the layer of cells that form the lining (endometrium) of the uterus.',
        'It is the most common type of uterine cancer and often detected at an early stage.',
        'Our AI-powered analysis helps pathologists identify and classify cancer cells with high accuracy.'
      ],
      icon: Book,
    },
    symptoms: {
      title: 'Common Symptoms',
      content: [
        'Unusual vaginal bleeding, spotting, or discharge',
        'Pelvic pain or pressure',
        'Difficulty or pain when urinating',
        'Pain during intercourse',
        'Unexplained weight loss'
      ],
      icon: AlertCircle,
    },
    diagnosis: {
      title: 'Diagnostic Process',
      content: [
        'Physical examination and medical history',
        'Transvaginal ultrasound',
        'Endometrial biopsy',
        'Hysteroscopy',
        'AI-assisted tissue analysis'
      ],
      icon: Microscope,
    },
    treatment: {
      title: 'Treatment Options',
      content: [
        'Surgery (hysterectomy)',
        'Radiation therapy',
        'Chemotherapy',
        'Hormone therapy',
        'Targeted therapy'
      ],
      icon: HeartPulse,
    },
  };

  return (
    <section className="py-16 bg-gradient-to-b from-white to-sky-50">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Understanding Endometrial Cancer</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Our platform combines advanced AI technology with medical expertise to improve detection and analysis of endometrial cancer.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card className="text-center h-full hover:shadow-lg transition-shadow duration-300">
                <CardHeader>
                  <div className={`w-16 h-16 ${stat.color} rounded-2xl mx-auto flex items-center justify-center mb-4 shadow-lg transform hover:scale-105 transition-transform`}>
                    {stat.icon && <stat.icon className="w-8 h-8 text-white" />}
                  </div>
                  <CardTitle className="text-xl font-bold">{stat.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</p>
                  <p className="text-gray-600">{stat.description}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <Tabs defaultValue="about" className="w-full">
          <TabsList className="grid w-full max-w-2xl mx-auto grid-cols-4">
            {Object.keys(tabContent).map((tab) => (
              <TabsTrigger
                key={tab}
                value={tab}
                className="data-[state=active]:bg-sky-50 data-[state=active]:text-sky-700"
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </TabsTrigger>
            ))}
          </TabsList>

          {Object.entries(tabContent).map(([key, { title, content, icon: Icon }]) => (
            <TabsContent key={key} value={key}>
              <Card>
                <CardHeader className="flex flex-row items-center gap-4">
                  {Icon && <Icon className="w-8 h-8 text-sky-600" />}
                  <CardTitle>{title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {content.map((item, index) => (
                      <motion.li
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex items-start"
                      >
                        <span className="w-2 h-2 mt-2 mr-3 rounded-full bg-sky-500" />
                        <span className="text-gray-600">{item}</span>
                      </motion.li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </section>
  );
};

export default InfoSection;
