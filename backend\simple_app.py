from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import uuid
import numpy as np
from PIL import Image
import shutil
import traceback
import time

app = Flask(__name__)
# Configure Flask for larger file uploads
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500 MB max upload
CORS(app, resources={r"/*": {"origins": "*"}})

# Configure upload folder
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
PATCHES_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'patches')

# Create directories if they don't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(PATCHES_FOLDER, exist_ok=True)

@app.route('/api/upload-wsi', methods=['POST'])
def upload_wsi():
    print("Received upload request")
    
    if 'file' not in request.files:
        print("No file part in request")
        return jsonify({'error': 'No file part'}), 400
    
    file = request.files['file']
    
    if file.filename == '':
        print("No selected file")
        return jsonify({'error': 'No selected file'}), 400
    
    print(f"Processing file: {file.filename}")
    
    # Generate a unique ID for this upload
    session_id = str(uuid.uuid4())
    session_folder = os.path.join(PATCHES_FOLDER, session_id)
    os.makedirs(session_folder, exist_ok=True)
    
    # Save the uploaded file
    file_path = os.path.join(UPLOAD_FOLDER, f"{session_id}_{file.filename}")
    file.save(file_path)
    print(f"Saved file to {file_path}")
    
    try:
        # Process the image and extract patches
        patches_info = extract_patches(file_path, session_folder)
        print(f"Extracted {len(patches_info)} patches")
        
        response_data = {
            'success': True,
            'sessionId': session_id,
            'patchesCount': len(patches_info),
            'patches': patches_info
        }
        
        return jsonify(response_data)
    except Exception as e:
        error_traceback = traceback.format_exc()
        print(f"Error processing image: {str(e)}")
        print(error_traceback)
        
        # Clean up any files that might have been created
        try:
            if os.path.exists(session_folder):
                shutil.rmtree(session_folder)
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception as cleanup_error:
            print(f"Error during cleanup: {str(cleanup_error)}")
            
        return jsonify({'error': str(e), 'traceback': error_traceback}), 500

def extract_patches(image_path, output_dir):
    """
    Extract patches from an image using PIL.
    """
    print(f"Extracting patches from {image_path}")
    
    # Check if file exists
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"File not found: {image_path}")
    
    try:
        # Load the image using PIL
        image = Image.open(image_path)
        print(f"Image format: {image.format}, Size: {image.size}, Mode: {image.mode}")
        
        # Convert to RGB if needed
        if image.mode != 'RGB':
            print(f"Converting image from {image.mode} to RGB")
            image = image.convert('RGB')
        
        # Get image dimensions
        width, height = image.size
        print(f"Image dimensions: {width}x{height}")
        
        # Define patch size
        patch_width, patch_height = 300, 300
        
        # Calculate number of patches
        num_patches_x = max(1, width // patch_width)
        num_patches_y = max(1, height // patch_height)
        print(f"Will extract from grid of {num_patches_x}x{num_patches_y} patches")
        
        patches_info = []
        patch_count = 0
        
        # Extract patches in a grid pattern
        max_rows = min(num_patches_y, 5)  # Limit to 5 rows for testing
        max_cols = min(num_patches_x, 5)  # Limit to 5 columns for testing
        print(f"Limited to {max_rows}x{max_cols} grid for testing")
        
        for y in range(max_rows):
            for x in range(max_cols):
                try:
                    # Calculate patch coordinates
                    x_start = x * patch_width
                    y_start = y * patch_height
                    x_end = min(x_start + patch_width, width)
                    y_end = min(y_start + patch_height, height)
                    
                    print(f"Extracting patch at ({x_start}, {y_start}) to ({x_end}, {y_end})")
                    
                    # Extract the patch
                    patch = image.crop((x_start, y_start, x_end, y_end))
                    
                    # Skip patches that are too small
                    if patch.width < 100 or patch.height < 100:
                        print(f"Skipping small patch: {patch.width}x{patch.height}")
                        continue
                    
                    # Save the patch
                    patch_filename = f"tissue_patch_{patch_count}.png"
                    patch_path = os.path.join(output_dir, patch_filename)
                    patch.save(patch_path)
                    print(f"Saved patch to {patch_path}")
                    
                    # Add patch info
                    patches_info.append({
                        'id': patch_count,
                        'filename': patch_filename,
                        'x': x_start,
                        'y': y_start,
                        'width': x_end - x_start,
                        'height': y_end - y_start,
                        'patchId': f"patch_{patch_count}.png"  # Add patchId for compatibility
                    })
                    
                    patch_count += 1
                    
                    # Limit to 20 patches for testing
                    if patch_count >= 20:
                        print(f"Reached maximum patch count of 20")
                        return patches_info
                except Exception as e:
                    print(f"Error extracting patch at ({x_start}, {y_start}): {str(e)}")
                    print(traceback.format_exc())
                    continue
        
        print(f"Extracted {patch_count} patches total")
        return patches_info
    except Exception as e:
        print(f"Error processing image: {str(e)}")
        print(traceback.format_exc())
        raise ValueError(f"Failed to process image: {str(e)}")

@app.route('/api/patches/<session_id>/<patch_filename>', methods=['GET'])
def get_patch(session_id, patch_filename):
    patch_folder = os.path.join(PATCHES_FOLDER, session_id)
    return send_from_directory(patch_folder, patch_filename)

@app.route('/api/cleanup/<session_id>', methods=['DELETE'])
def cleanup_session(session_id):
    """Clean up session files when analysis is complete"""
    session_folder = os.path.join(PATCHES_FOLDER, session_id)
    
    if os.path.exists(session_folder):
        shutil.rmtree(session_folder)
    
    # Also remove the original image file
    for filename in os.listdir(UPLOAD_FOLDER):
        if filename.startswith(f"{session_id}_"):
            os.remove(os.path.join(UPLOAD_FOLDER, filename))
    
    return jsonify({'success': True})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080, debug=True)
