import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { motion } from 'framer-motion';
import { MicroscopeIcon, HelpCircleIcon, LogOutIcon, LineChartIcon } from 'lucide-react';

const Header = () => {
  const navigate = useNavigate();
  
  const handleLogout = () => {
    localStorage.removeItem('isLoggedIn');
    navigate('/login');
  };

  return (
    <motion.header 
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gradient-to-r from-sky-50 via-blue-50 to-indigo-50 shadow-md border-b border-sky-100"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex justify-between items-center">
          <motion.div 
            className="flex items-center space-x-4"
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <div className="w-10 h-10 bg-gradient-to-br from-sky-500 to-indigo-500 rounded-lg flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform">
              <MicroscopeIcon className="w-6 h-6 text-white" />
            </div>
            <div>
              <motion.h1 
                className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-sky-700 to-indigo-700"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
              >
                Endometrial Cancer Analysis Platform
              </motion.h1>
              <motion.p 
                className="text-sm text-sky-600"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
              >
                Advanced Tissue Analysis & Diagnostics
              </motion.p>
            </div>
          </motion.div>
          
          <motion.div 
            className="flex items-center space-x-3"
            initial={{ x: 20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <Button
              variant="ghost"
              className="text-sky-700 hover:text-sky-800 hover:bg-sky-50 transition-colors duration-200"
              onClick={() => navigate('/dashboard')}
            >
              <LineChartIcon className="w-4 h-4 mr-2" />
              Dashboard
            </Button>
            <Button
              variant="ghost"
              className="text-sky-700 hover:text-sky-800 hover:bg-sky-50 transition-colors duration-200"
              onClick={() => {/* Add help modal */}}
            >
              <HelpCircleIcon className="w-4 h-4 mr-2" />
              Help
            </Button>
            <Separator orientation="vertical" className="h-6 bg-sky-200" />
            <Button
              variant="outline"
              className="border-sky-200 hover:border-sky-300 text-sky-700 hover:text-sky-800 hover:bg-sky-50 transition-all duration-200"
              onClick={handleLogout}
            >
              <LogOutIcon className="w-4 h-4 mr-2" />
              Sign Out
            </Button>
          </motion.div>
        </div>
      </div>
      <Separator className="bg-sky-100" />
    </motion.header>
  );
};

export default Header;
