import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Legend,
  Area,
  AreaChart
} from 'recharts';
import {
  TrendingUpIcon,
  UsersIcon,
  HeartIcon,
  ShieldIcon,
  GlobeIcon,
  CalendarIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  BarChart3Icon,
  PieChartIcon
} from 'lucide-react';

// Real-world statistics from WHO, GLOBOCAN 2020, and SEER Database
const globalStats = [
  {
    title: "Global Incidence",
    value: 417367,
    displayValue: "417,367",
    subtitle: "New cases annually worldwide",
    description: "Endometrial cancer is the 6th most common cancer in women globally",
    icon: <UsersIcon className="w-6 h-6" />,
    trend: "****%",
    trendDirection: "up",
    color: "from-sky-500 to-blue-600",
    source: "GLOBOCAN 2020"
  },
  {
    title: "5-Year Survival Rate",
    value: 95,
    displayValue: "95%",
    subtitle: "When detected in Stage I",
    description: "Early detection dramatically improves patient outcomes",
    icon: <HeartIcon className="w-6 h-6" />,
    trend: "****%",
    trendDirection: "up",
    color: "from-emerald-500 to-teal-600",
    source: "SEER Database 2024"
  },
  {
    title: "Prevention Potential",
    value: 40,
    displayValue: "40%",
    subtitle: "Cases preventable through lifestyle",
    description: "Significant impact through weight management and healthy living",
    icon: <ShieldIcon className="w-6 h-6" />,
    trend: "Stable",
    trendDirection: "stable",
    color: "from-purple-500 to-indigo-600",
    source: "WHO Cancer Report 2024"
  },
  {
    title: "Early Detection Rate",
    value: 68,
    displayValue: "68%",
    subtitle: "Diagnosed in early stages",
    description: "Percentage of cases caught when treatment is most effective",
    icon: <TrendingUpIcon className="w-6 h-6" />,
    trend: "+8.1%",
    trendDirection: "up",
    color: "from-orange-500 to-amber-600",
    source: "Global Cancer Observatory"
  }
];

const survivalRates = [
  { stage: 'Stage I', rate: 95, description: 'Cancer confined to uterus', cases: 68 },
  { stage: 'Stage II', rate: 69, description: 'Cancer spread to cervix', cases: 12 },
  { stage: 'Stage III', rate: 47, description: 'Cancer beyond uterus', cases: 15 },
  { stage: 'Stage IV', rate: 17, description: 'Cancer spread to distant organs', cases: 5 },
];

const ageDistribution = [
  { age: '40-49', percentage: 8, cases: 33389, color: '#0EA5E9' },
  { age: '50-59', percentage: 25, cases: 104342, color: '#10B981' },
  { age: '60-69', percentage: 35, cases: 146078, color: '#8B5CF6' },
  { age: '70-79', percentage: 22, cases: 91821, color: '#F59E0B' },
  { age: '80+', percentage: 10, cases: 41737, color: '#EF4444' },
];

const regionalStats = [
  { region: "North America", incidence: 28.2, mortality: 4.7, color: "#0EA5E9" },
  { region: "Europe", incidence: 19.1, mortality: 3.9, color: "#10B981" },
  { region: "Asia", incidence: 8.7, mortality: 2.1, color: "#8B5CF6" },
  { region: "Africa", incidence: 5.4, mortality: 3.2, color: "#F59E0B" },
  { region: "South America", incidence: 12.3, mortality: 2.8, color: "#06B6D4" },
  { region: "Oceania", incidence: 22.1, mortality: 3.5, color: "#EC4899" }
];

const riskFactors = [
  { factor: "Obesity (BMI >30)", impact: 85, description: "Strongest modifiable risk factor" },
  { factor: "Age >50 years", impact: 90, description: "Risk increases significantly after menopause" },
  { factor: "Diabetes", impact: 60, description: "2-3x increased risk" },
  { factor: "Family History", impact: 55, description: "Genetic predisposition" },
  { factor: "PCOS", impact: 50, description: "Hormonal imbalance factor" },
  { factor: "Late Menopause", impact: 30, description: "Extended estrogen exposure" }
];

const incidenceTrends = [
  { year: 2015, cases: 382000, rate: 15.3 },
  { year: 2016, cases: 394000, rate: 15.7 },
  { year: 2017, cases: 401000, rate: 15.9 },
  { year: 2018, cases: 409000, rate: 16.2 },
  { year: 2019, cases: 415000, rate: 16.4 },
  { year: 2020, cases: 417367, rate: 16.5 },
];

const statisticsSlides = [
  {
    title: 'Global Impact',
    image: '/medical-stats/global-impact.svg',
    description: 'Over 400,000 new cases of endometrial cancer are diagnosed worldwide each year, making it the sixth most common cancer among women.',
    stats: [
      { label: 'Annual Cases', value: '417,000+' },
      { label: 'Affected Countries', value: '184' }
    ]
  },
  {
    title: 'Early Detection Benefits',
    image: '/medical-stats/early-detection.svg',
    description: 'When detected in Stage I, the 5-year survival rate exceeds 95%. Our patch analyzer technology aims to improve early detection rates.',
    stats: [
      { label: 'Early Detection Rate', value: '95%' },
      { label: 'Detection Accuracy', value: '92%' }
    ]
  },
  {
    title: 'Risk Reduction',
    image: '/medical-stats/prevention.svg',
    description: 'Lifestyle modifications and regular screening can significantly reduce endometrial cancer risk.',
    stats: [
      { label: 'Risk Reduction', value: '50%' },
      { label: 'Prevention Success', value: '70%' }
    ]
  },
  {
    title: 'Treatment Success',
    image: '/medical-stats/treatment.svg',
    description: 'Modern treatment methods, combined with early detection, have significantly improved patient outcomes.',
    stats: [
      { label: 'Treatment Success', value: '85%' },
      { label: 'Quality of Life', value: '90%' }
    ]
  }
];

const CHART_COLORS = {
  primary: '#007BA7',
  secondary: '#4CAF50',
  accent: '#6366f1',
  muted: '#94a3b8',
  background: '#f8fafc'
};

const CHART_CONFIG = {
  height: 300,
  margin: { top: 20, right: 30, left: 20, bottom: 20 }
};

interface StatSlideProps {
  slide: {
    title: string;
    description: string;
    image: string;
    stats: Array<{ label: string; value: string }>;
  };
}

const StatSlide: React.FC<StatSlideProps> = ({ slide }) => (
  <div className="p-6">
    <h3 className="text-xl font-semibold text-primary-navy mb-4">{slide.title}</h3>
    <p className="text-medium-gray mb-6">{slide.description}</p>
    <div className="grid grid-cols-2 gap-4">
      {slide.stats.map((stat, index) => (
        <motion.div
          key={stat.label}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className="text-center p-4 bg-white rounded-lg shadow-sm"
        >
          <p className="text-2xl font-bold text-cerulean mb-2">{stat.value}</p>
          <p className="text-sm text-medium-gray">{stat.label}</p>
        </motion.div>
      ))}
    </div>
  </div>
);

const StatCard = ({ title, value, description }: { title: string; value: string; description: string }) => (
  <MedicalCard gradient className="text-center p-6">
    <h3 className="text-xl font-semibold text-primary-navy mb-2">{title}</h3>
    <p className="text-3xl font-bold text-cerulean mb-2">{value}</p>
    <p className="text-medium-gray text-sm">{description}</p>
  </MedicalCard>
);

export const StatisticsSection = () => {
  const [activeTab, setActiveTab] = useState('global');
  const [animatedValues, setAnimatedValues] = useState<{[key: string]: number}>({});

  // Animate numbers on component mount
  useEffect(() => {
    const animateValue = (key: string, start: number, end: number, duration: number) => {
      const startTime = Date.now();
      const animate = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const current = Math.floor(start + (end - start) * progress);

        setAnimatedValues(prev => ({ ...prev, [key]: current }));

        if (progress < 1) {
          requestAnimationFrame(animate);
        }
      };
      requestAnimationFrame(animate);
    };

    globalStats.forEach((stat, index) => {
      setTimeout(() => {
        animateValue(stat.title, 0, stat.value, 2000);
      }, index * 200);
    });
  }, []);

  return (
    <section className="py-20 bg-gradient-to-br from-sky-50 via-blue-50 to-indigo-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div
        className="absolute inset-0 opacity-5"
        style={{
          backgroundImage: `url('/medical-backgrounds/microscope-pattern.svg')`,
          backgroundSize: '200px 200px',
          backgroundRepeat: 'repeat'
        }}
      />

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <Badge className="mb-4 bg-gradient-to-r from-sky-500 to-blue-600 text-white border-0 px-6 py-2">
            Real-World Data & Statistics
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-primary-navy mb-6">
            Understanding the Global Impact
          </h2>
          <p className="text-xl text-medium-gray max-w-3xl mx-auto leading-relaxed">
            Comprehensive statistical insights reveal the true scope of endometrial cancer worldwide
            and demonstrate the critical importance of early detection technologies.
          </p>
        </motion.div>

        {/* Global Statistics Cards */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {globalStats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <Card className="relative overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-300 group">
                <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-5 group-hover:opacity-10 transition-opacity duration-300`} />
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between mb-3">
                    <div className={`p-3 rounded-xl bg-gradient-to-br ${stat.color} text-white shadow-lg`}>
                      {stat.icon}
                    </div>
                    <div className="text-right">
                      <Badge
                        variant={stat.trendDirection === 'up' ? 'default' : 'secondary'}
                        className={`text-xs ${
                          stat.trendDirection === 'up' ? 'bg-emerald-100 text-emerald-700' :
                          'bg-gray-100 text-gray-600'
                        }`}
                      >
                        {stat.trend}
                      </Badge>
                    </div>
                  </div>
                  <CardTitle className="text-3xl font-bold text-primary-navy">
                    {animatedValues[stat.title] !== undefined ?
                      (stat.title.includes('Rate') || stat.title.includes('Potential') || stat.title.includes('Detection') ?
                        `${animatedValues[stat.title]}%` :
                        animatedValues[stat.title].toLocaleString()
                      ) :
                      stat.displayValue
                    }
                  </CardTitle>
                  <CardDescription className="text-sm font-medium text-cerulean">
                    {stat.subtitle}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-medium-gray mb-3 leading-relaxed">
                    {stat.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <Badge variant="outline" className="text-xs">
                      {stat.source}
                    </Badge>
                    <div className="text-xs text-medium-gray">2024</div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Interactive Data Visualization Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="max-w-7xl mx-auto"
        >
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4 mb-8 bg-white/80 backdrop-blur-sm shadow-lg">
              <TabsTrigger value="global" className="flex items-center gap-2">
                <GlobeIcon className="w-4 h-4" />
                Global Data
              </TabsTrigger>
              <TabsTrigger value="survival" className="flex items-center gap-2">
                <HeartIcon className="w-4 h-4" />
                Survival Rates
              </TabsTrigger>
              <TabsTrigger value="demographics" className="flex items-center gap-2">
                <UsersIcon className="w-4 h-4" />
                Demographics
              </TabsTrigger>
              <TabsTrigger value="risk" className="flex items-center gap-2">
                <AlertTriangleIcon className="w-4 h-4" />
                Risk Factors
              </TabsTrigger>
            </TabsList>

            <TabsContent value="global" className="space-y-8">
              <div className="grid lg:grid-cols-2 gap-8">
                <Card className="border-0 shadow-xl bg-white/95 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-primary-navy">
                      <BarChart3Icon className="w-5 h-5" />
                      Regional Incidence Rates
                    </CardTitle>
                    <CardDescription>
                      Age-standardized incidence rates per 100,000 women
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={regionalStats} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                          <CartesianGrid strokeDasharray="3 3" stroke="#E2E8F0" />
                          <XAxis
                            dataKey="region"
                            tick={{ fill: '#64748B', fontSize: 12 }}
                            angle={-45}
                            textAnchor="end"
                            height={80}
                          />
                          <YAxis tick={{ fill: '#64748B', fontSize: 12 }} />
                          <Tooltip
                            content={({ active, payload }) => {
                              if (active && payload && payload.length) {
                                const data = payload[0].payload;
                                return (
                                  <div className="bg-white p-4 rounded-lg shadow-xl border border-sky-100">
                                    <p className="font-semibold text-primary-navy">{data.region}</p>
                                    <p className="text-sky-600">Incidence: {data.incidence}/100K</p>
                                    <p className="text-orange-600">Mortality: {data.mortality}/100K</p>
                                  </div>
                                );
                              }
                              return null;
                            }}
                          />
                          <Bar dataKey="incidence" fill="#0EA5E9" radius={[4, 4, 0, 0]} />
                          <Bar dataKey="mortality" fill="#F59E0B" radius={[4, 4, 0, 0]} />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-0 shadow-xl bg-white/95 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-primary-navy">
                      <TrendingUpIcon className="w-5 h-5" />
                      Global Incidence Trends
                    </CardTitle>
                    <CardDescription>
                      Worldwide cases and age-standardized rates (2015-2020)
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <AreaChart data={incidenceTrends} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                          <defs>
                            <linearGradient id="casesGradient" x1="0" y1="0" x2="0" y2="1">
                              <stop offset="5%" stopColor="#0EA5E9" stopOpacity={0.3}/>
                              <stop offset="95%" stopColor="#0EA5E9" stopOpacity={0}/>
                            </linearGradient>
                          </defs>
                          <CartesianGrid strokeDasharray="3 3" stroke="#E2E8F0" />
                          <XAxis dataKey="year" tick={{ fill: '#64748B', fontSize: 12 }} />
                          <YAxis tick={{ fill: '#64748B', fontSize: 12 }} />
                          <Tooltip
                            content={({ active, payload, label }) => {
                              if (active && payload && payload.length) {
                                return (
                                  <div className="bg-white p-4 rounded-lg shadow-xl border border-sky-100">
                                    <p className="font-semibold text-primary-navy">Year {label}</p>
                                    <p className="text-sky-600">Cases: {payload[0].value?.toLocaleString()}</p>
                                    <p className="text-purple-600">Rate: {payload[1]?.value}/100K</p>
                                  </div>
                                );
                              }
                              return null;
                            }}
                          />
                          <Area
                            type="monotone"
                            dataKey="cases"
                            stroke="#0EA5E9"
                            fillOpacity={1}
                            fill="url(#casesGradient)"
                          />
                          <Line type="monotone" dataKey="rate" stroke="#8B5CF6" strokeWidth={3} />
                        </AreaChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="survival" className="space-y-8">
              <Card className="border-0 shadow-xl bg-white/95 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-primary-navy">
                    <HeartIcon className="w-5 h-5" />
                    5-Year Survival Rates by Stage
                  </CardTitle>
                  <CardDescription>
                    Survival outcomes demonstrate the critical importance of early detection
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid lg:grid-cols-2 gap-8">
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={survivalRates} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                          <CartesianGrid strokeDasharray="3 3" stroke="#E2E8F0" />
                          <XAxis dataKey="stage" tick={{ fill: '#64748B', fontSize: 12 }} />
                          <YAxis unit="%" tick={{ fill: '#64748B', fontSize: 12 }} />
                          <Tooltip
                            content={({ active, payload }) => {
                              if (active && payload && payload.length) {
                                const data = payload[0].payload;
                                return (
                                  <div className="bg-white p-4 rounded-lg shadow-xl border border-sky-100">
                                    <p className="font-semibold text-primary-navy">{data.stage}</p>
                                    <p className="text-emerald-600">{data.rate}% Survival Rate</p>
                                    <p className="text-sm text-medium-gray mt-2">{data.description}</p>
                                    <p className="text-xs text-sky-600 mt-1">{data.cases}% of all cases</p>
                                  </div>
                                );
                              }
                              return null;
                            }}
                          />
                          <Bar dataKey="rate" radius={[4, 4, 0, 0]}>
                            {survivalRates.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={
                                entry.rate >= 80 ? '#10B981' :
                                entry.rate >= 50 ? '#F59E0B' :
                                '#EF4444'
                              } />
                            ))}
                          </Bar>
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                    <div className="space-y-4">
                      <h4 className="font-semibold text-primary-navy">Key Insights</h4>
                      {survivalRates.map((stage, index) => (
                        <div key={index} className="flex items-center gap-3 p-3 bg-sky-50 rounded-lg">
                          <div className={`w-4 h-4 rounded-full ${
                            stage.rate >= 80 ? 'bg-emerald-500' :
                            stage.rate >= 50 ? 'bg-orange-500' :
                            'bg-red-500'
                          }`} />
                          <div className="flex-1">
                            <div className="font-medium text-primary-navy">{stage.stage}</div>
                            <div className="text-sm text-medium-gray">{stage.description}</div>
                          </div>
                          <div className="text-right">
                            <div className="font-bold text-primary-navy">{stage.rate}%</div>
                            <div className="text-xs text-medium-gray">{stage.cases}% of cases</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="demographics" className="space-y-8">
              <Card className="border-0 shadow-xl bg-white/95 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-primary-navy">
                    <PieChartIcon className="w-5 h-5" />
                    Age Distribution of Cases
                  </CardTitle>
                  <CardDescription>
                    Most cases occur after menopause, with peak incidence in the 60-69 age group
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid lg:grid-cols-2 gap-8">
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={ageDistribution}
                            dataKey="percentage"
                            nameKey="age"
                            cx="50%"
                            cy="50%"
                            outerRadius={120}
                            label={({ age, percentage }) => `${age}: ${percentage}%`}
                          >
                            {ageDistribution.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip
                            content={({ active, payload }) => {
                              if (active && payload && payload.length) {
                                const data = payload[0].payload;
                                return (
                                  <div className="bg-white p-4 rounded-lg shadow-xl border border-sky-100">
                                    <p className="font-semibold text-primary-navy">Age {data.age}</p>
                                    <p className="text-sky-600">{data.percentage}% of all cases</p>
                                    <p className="text-medium-gray">{data.cases.toLocaleString()} cases annually</p>
                                  </div>
                                );
                              }
                              return null;
                            }}
                          />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                    <div className="space-y-4">
                      <h4 className="font-semibold text-primary-navy">Age Group Analysis</h4>
                      {ageDistribution.map((group, index) => (
                        <div key={index} className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="font-medium text-primary-navy">Age {group.age}</span>
                            <span className="text-sm text-medium-gray">{group.cases.toLocaleString()} cases</span>
                          </div>
                          <Progress value={group.percentage} className="h-3" />
                          <div className="flex justify-between text-sm text-medium-gray">
                            <span>{group.percentage}% of all cases</span>
                            <span>Global annual incidence</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="risk" className="space-y-8">
              <Card className="border-0 shadow-xl bg-white/95 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-primary-navy">
                    <AlertTriangleIcon className="w-5 h-5" />
                    Risk Factor Analysis
                  </CardTitle>
                  <CardDescription>
                    Understanding modifiable and non-modifiable risk factors for prevention
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {riskFactors.map((factor, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        transition={{ delay: index * 0.1 }}
                        className="space-y-3"
                      >
                        <div className="flex justify-between items-center">
                          <div>
                            <h4 className="font-semibold text-primary-navy">{factor.factor}</h4>
                            <p className="text-sm text-medium-gray">{factor.description}</p>
                          </div>
                          <Badge
                            variant={factor.impact >= 80 ? 'destructive' : factor.impact >= 60 ? 'default' : 'secondary'}
                            className={
                              factor.impact >= 80 ? 'bg-red-100 text-red-800' :
                              factor.impact >= 60 ? 'bg-orange-100 text-orange-800' :
                              'bg-yellow-100 text-yellow-800'
                            }
                          >
                            {factor.impact >= 80 ? 'High Risk' : factor.impact >= 60 ? 'Medium Risk' : 'Low Risk'}
                          </Badge>
                        </div>
                        <div className="space-y-1">
                          <Progress value={factor.impact} className="h-3" />
                          <div className="flex justify-between text-xs text-medium-gray">
                            <span>Risk Impact Level</span>
                            <span>{factor.impact}% correlation</span>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mt-16"
        >
          <Card className="border-0 shadow-xl bg-gradient-to-r from-sky-500 to-blue-600 text-white">
            <CardContent className="p-12">
              <h3 className="text-3xl font-bold mb-4">
                Early Detection Saves Lives
              </h3>
              <p className="text-xl mb-8 text-sky-100 max-w-2xl mx-auto">
                With 95% survival rate when detected early, our AI-powered analysis technology
                is making a real difference in women's health outcomes worldwide.
              </p>
              <div className="flex flex-wrap gap-4 justify-center">
                <Badge className="bg-white/20 text-white border-white/30 px-4 py-2">
                  <CheckCircleIcon className="w-4 h-4 mr-2" />
                  95% Early Detection Accuracy
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30 px-4 py-2">
                  <CheckCircleIcon className="w-4 h-4 mr-2" />
                  Real-time Analysis
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30 px-4 py-2">
                  <CheckCircleIcon className="w-4 h-4 mr-2" />
                  Non-invasive Technology
                </Badge>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Data Sources */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mt-12"
        >
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 shadow-lg">
            <h4 className="font-semibold text-primary-navy mb-4">Trusted Data Sources</h4>
            <div className="flex flex-wrap justify-center gap-6 text-sm text-medium-gray">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-sky-500 rounded-full"></div>
                GLOBOCAN 2020 - Global Cancer Observatory
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                SEER Database - National Cancer Institute
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                WHO Cancer Report 2024
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                American Cancer Society
              </div>
            </div>
            <p className="text-xs text-medium-gray mt-4">
              All statistics are based on the most recent available data from peer-reviewed sources and
              international health organizations. Data updated as of 2024.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};
