<svg width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#06B6D4;stop-opacity:0.2" />
    </linearGradient>
    <linearGradient id="deviceGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#10B981" />
      <stop offset="100%" style="stop-color:#06B6D4" />
    </linearGradient>
    <linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0EA5E9" />
      <stop offset="50%" style="stop-color:#10B981" />
      <stop offset="100%" style="stop-color:#8B5CF6" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="400" fill="url(#bgGradient2)"/>
  
  <!-- Subtle Pattern -->
  <pattern id="comfortPattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
    <circle cx="30" cy="30" r="2" fill="#10B981" opacity="0.1"/>
    <circle cx="15" cy="15" r="1" fill="#06B6D4" opacity="0.1"/>
    <circle cx="45" cy="45" r="1" fill="#06B6D4" opacity="0.1"/>
  </pattern>
  <rect width="600" height="400" fill="url(#comfortPattern)"/>
  
  <!-- Medical Device/Patch -->
  <g transform="translate(200, 150)">
    <!-- Main Device Body -->
    <rect x="0" y="0" width="200" height="100" rx="20" fill="url(#deviceGradient)" opacity="0.8"/>
    <rect x="10" y="10" width="180" height="80" rx="15" fill="rgba(255,255,255,0.9)" stroke="#10B981" stroke-width="2"/>
    
    <!-- Device Screen -->
    <rect x="30" y="30" width="140" height="40" rx="8" fill="#1e3a8a" opacity="0.8"/>
    
    <!-- Screen Content -->
    <g transform="translate(35, 35)">
      <!-- Heartbeat Line -->
      <path d="M0 15 L20 15 L25 5 L30 25 L35 15 L55 15 L60 8 L65 22 L70 15 L90 15 L95 10 L100 20 L105 15 L130 15" 
            stroke="#10B981" stroke-width="2" fill="none"/>
      
      <!-- Status Indicators -->
      <circle cx="10" cy="25" r="3" fill="#10B981"/>
      <circle cx="120" cy="25" r="3" fill="#0EA5E9"/>
      
      <!-- Text -->
      <text x="70" y="8" text-anchor="middle" fill="#10B981" font-family="Arial, sans-serif" font-size="8">ACTIVE</text>
    </g>
    
    <!-- Device Buttons -->
    <circle cx="170" cy="35" r="8" fill="#0EA5E9" opacity="0.7"/>
    <circle cx="170" cy="55" r="8" fill="#8B5CF6" opacity="0.7"/>
    
    <!-- Connection Ports -->
    <rect x="190" y="45" width="15" height="10" rx="2" fill="#64748B" opacity="0.5"/>
  </g>
  
  <!-- Wireless Signals -->
  <g transform="translate(300, 100)" opacity="0.6">
    <!-- Signal Waves -->
    <path d="M50 50 Q70 30 90 50" stroke="url(#waveGradient)" stroke-width="3" fill="none"/>
    <path d="M50 50 Q75 25 100 50" stroke="url(#waveGradient)" stroke-width="2" fill="none" opacity="0.7"/>
    <path d="M50 50 Q80 20 110 50" stroke="url(#waveGradient)" stroke-width="1" fill="none" opacity="0.5"/>
    
    <!-- Signal Source -->
    <circle cx="50" cy="50" r="5" fill="#0EA5E9"/>
    <circle cx="50" cy="50" r="3" fill="#ffffff"/>
  </g>
  
  <!-- Comfort Indicators -->
  <g transform="translate(80, 80)">
    <!-- Comfort Icon 1 -->
    <circle cx="30" cy="30" r="20" fill="rgba(16, 185, 129, 0.2)" stroke="#10B981" stroke-width="2"/>
    <path d="M20 30 Q30 20 40 30 Q30 40 20 30" fill="#10B981"/>
    <text x="30" y="55" text-anchor="middle" fill="#10B981" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Comfort</text>
    
    <!-- Comfort Icon 2 -->
    <g transform="translate(80, 0)">
      <circle cx="30" cy="30" r="20" fill="rgba(14, 165, 233, 0.2)" stroke="#0EA5E9" stroke-width="2"/>
      <rect x="25" y="25" width="10" height="10" rx="2" fill="#0EA5E9"/>
      <circle cx="30" cy="30" r="3" fill="#ffffff"/>
      <text x="30" y="55" text-anchor="middle" fill="#0EA5E9" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Safe</text>
    </g>
    
    <!-- Comfort Icon 3 -->
    <g transform="translate(160, 0)">
      <circle cx="30" cy="30" r="20" fill="rgba(139, 92, 246, 0.2)" stroke="#8B5CF6" stroke-width="2"/>
      <path d="M25 35 L30 30 L35 35 M30 25 L30 35" stroke="#8B5CF6" stroke-width="2" fill="none"/>
      <text x="30" y="55" text-anchor="middle" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Easy</text>
    </g>
  </g>
  
  <!-- Patient Silhouette -->
  <g transform="translate(450, 200)" opacity="0.4">
    <!-- Body -->
    <ellipse cx="25" cy="80" rx="20" ry="40" fill="#64748B"/>
    <!-- Head -->
    <circle cx="25" cy="25" r="15" fill="#64748B"/>
    <!-- Arms -->
    <ellipse cx="10" cy="60" rx="8" ry="25" fill="#64748B"/>
    <ellipse cx="40" cy="60" rx="8" ry="25" fill="#64748B"/>
    
    <!-- Comfort Aura -->
    <circle cx="25" cy="60" r="50" fill="none" stroke="#10B981" stroke-width="2" opacity="0.3"/>
    <circle cx="25" cy="60" r="60" fill="none" stroke="#10B981" stroke-width="1" opacity="0.2"/>
  </g>
  
  <!-- Feature Highlights -->
  <g transform="translate(50, 300)">
    <rect x="0" y="0" width="500" height="80" rx="15" fill="rgba(255,255,255,0.9)" stroke="#10B981" stroke-width="2"/>
    
    <!-- Feature 1 -->
    <g transform="translate(30, 20)">
      <circle cx="15" cy="15" r="12" fill="#10B981" opacity="0.2"/>
      <text x="15" y="19" text-anchor="middle" fill="#10B981" font-family="Arial, sans-serif" font-size="16" font-weight="bold">✓</text>
      <text x="15" y="35" text-anchor="middle" fill="#64748B" font-family="Arial, sans-serif" font-size="9">Non-Invasive</text>
    </g>
    
    <!-- Feature 2 -->
    <g transform="translate(150, 20)">
      <circle cx="15" cy="15" r="12" fill="#0EA5E9" opacity="0.2"/>
      <text x="15" y="19" text-anchor="middle" fill="#0EA5E9" font-family="Arial, sans-serif" font-size="16" font-weight="bold">✓</text>
      <text x="15" y="35" text-anchor="middle" fill="#64748B" font-family="Arial, sans-serif" font-size="9">Comfortable</text>
    </g>
    
    <!-- Feature 3 -->
    <g transform="translate(270, 20)">
      <circle cx="15" cy="15" r="12" fill="#8B5CF6" opacity="0.2"/>
      <text x="15" y="19" text-anchor="middle" fill="#8B5CF6" font-family="Arial, sans-serif" font-size="16" font-weight="bold">✓</text>
      <text x="15" y="35" text-anchor="middle" fill="#64748B" font-family="Arial, sans-serif" font-size="9">Accurate</text>
    </g>
    
    <!-- Feature 4 -->
    <g transform="translate(390, 20)">
      <circle cx="15" cy="15" r="12" fill="#F59E0B" opacity="0.2"/>
      <text x="15" y="19" text-anchor="middle" fill="#F59E0B" font-family="Arial, sans-serif" font-size="16" font-weight="bold">✓</text>
      <text x="15" y="35" text-anchor="middle" fill="#64748B" font-family="Arial, sans-serif" font-size="9">Accessible</text>
    </g>
  </g>
</svg>
