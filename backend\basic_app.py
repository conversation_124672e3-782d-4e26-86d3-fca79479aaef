from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import uuid
import shutil
import traceback

app = Flask(__name__)
# Configure Flask for larger file uploads
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500 MB max upload
CORS(app, resources={r"/*": {"origins": "*"}})

# Configure upload folder
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
PATCHES_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'patches')
SAMPLE_PATCHES_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'sample_patches')

# Create directories if they don't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(PATCHES_FOLDER, exist_ok=True)
os.makedirs(SAMPLE_PATCHES_FOLDER, exist_ok=True)

# Create some sample patches if they don't exist
def create_sample_patches():
    if not os.listdir(SAMPLE_PATCHES_FOLDER):
        print("Creating sample patches...")
        from PIL import Image
        import numpy as np

        # Create 5 sample patches with different colors
        colors = [
            (255, 0, 0),    # Red
            (0, 255, 0),    # Green
            (0, 0, 255),    # Blue
            (255, 255, 0),  # Yellow
            (255, 0, 255)   # Magenta
        ]

        for i, color in enumerate(colors):
            # Create a colored image
            img = Image.new('RGB', (300, 300), color)

            # Add some text
            from PIL import ImageDraw, ImageFont
            draw = ImageDraw.Draw(img)
            try:
                font = ImageFont.truetype("arial.ttf", 36)
            except:
                font = ImageFont.load_default()

            draw.text((75, 150), f"Patch {i+1}", fill=(255, 255, 255), font=font)

            # Save the image
            img.save(os.path.join(SAMPLE_PATCHES_FOLDER, f"sample_patch_{i}.png"))

        print(f"Created {len(colors)} sample patches")

# Create sample patches on startup
create_sample_patches()

@app.route('/api/test', methods=['GET'])
def test_endpoint():
    """Simple test endpoint to verify the server is accessible"""
    print("Test endpoint accessed")
    return jsonify({'status': 'ok', 'message': 'Server is running'})

@app.route('/upload-wsi', methods=['POST'])
@app.route('/api/upload-wsi', methods=['POST'])
def upload_wsi():
    print("Received upload request")
    print(f"Request method: {request.method}")
    print(f"Request path: {request.path}")
    print(f"Request headers: {dict(request.headers)}")

    if 'file' not in request.files:
        print("No file part in request")
        return jsonify({'error': 'No file part'}), 400

    file = request.files['file']

    if file.filename == '':
        print("No selected file")
        return jsonify({'error': 'No selected file'}), 400

    print(f"Processing file: {file.filename}")

    # Generate a unique ID for this upload
    session_id = str(uuid.uuid4())
    session_folder = os.path.join(PATCHES_FOLDER, session_id)
    os.makedirs(session_folder, exist_ok=True)

    # Save the uploaded file
    file_path = os.path.join(UPLOAD_FOLDER, f"{session_id}_{file.filename}")
    file.save(file_path)
    print(f"Saved file to {file_path}")

    try:
        # Instead of processing the image, just copy the sample patches
        patches_info = []

        # Copy sample patches to the session folder
        for i, filename in enumerate(os.listdir(SAMPLE_PATCHES_FOLDER)):
            src_path = os.path.join(SAMPLE_PATCHES_FOLDER, filename)
            dst_path = os.path.join(session_folder, f"tissue_patch_{i}.png")
            shutil.copy(src_path, dst_path)

            # Add patch info
            patches_info.append({
                'id': i,
                'filename': f"tissue_patch_{i}.png",
                'x': i * 300,
                'y': 0,
                'width': 300,
                'height': 300,
                'patchId': f"patch_{i}.png"  # Add patchId for compatibility
            })

        print(f"Copied {len(patches_info)} sample patches")

        response_data = {
            'success': True,
            'sessionId': session_id,
            'patchesCount': len(patches_info),
            'patches': patches_info
        }

        return jsonify(response_data)
    except Exception as e:
        error_traceback = traceback.format_exc()
        print(f"Error processing image: {str(e)}")
        print(error_traceback)

        # Clean up any files that might have been created
        try:
            if os.path.exists(session_folder):
                shutil.rmtree(session_folder)
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception as cleanup_error:
            print(f"Error during cleanup: {str(cleanup_error)}")

        return jsonify({'error': str(e), 'traceback': error_traceback}), 500

@app.route('/api/patches/<session_id>/<patch_filename>', methods=['GET'])
def get_patch(session_id, patch_filename):
    patch_folder = os.path.join(PATCHES_FOLDER, session_id)
    print(f"Requested patch: {patch_filename} from session {session_id}")
    print(f"Looking in folder: {patch_folder}")

    # Check if the file exists
    file_path = os.path.join(patch_folder, patch_filename)
    if os.path.exists(file_path):
        print(f"File found: {file_path}")
    else:
        print(f"File not found: {file_path}")
        # If the file doesn't exist in the session folder, try to serve from sample patches
        sample_path = os.path.join(SAMPLE_PATCHES_FOLDER, f"sample_patch_{0}.png")
        if os.path.exists(sample_path):
            print(f"Serving sample patch instead: {sample_path}")
            return send_from_directory(SAMPLE_PATCHES_FOLDER, f"sample_patch_{0}.png")

    try:
        return send_from_directory(patch_folder, patch_filename)
    except Exception as e:
        print(f"Error serving file: {str(e)}")
        # Return a fallback image
        return send_from_directory(SAMPLE_PATCHES_FOLDER, f"sample_patch_{0}.png")

@app.route('/api/sample-patches/<filename>', methods=['GET'])
def get_sample_patch(filename):
    """Serve sample patches directly"""
    print(f"Requested sample patch: {filename}")
    return send_from_directory(SAMPLE_PATCHES_FOLDER, filename)

@app.route('/api/cleanup/<session_id>', methods=['DELETE'])
def cleanup_session(session_id):
    """Clean up session files when analysis is complete"""
    session_folder = os.path.join(PATCHES_FOLDER, session_id)
    print(f"Cleaning up session: {session_id}")

    if os.path.exists(session_folder):
        print(f"Removing session folder: {session_folder}")
        shutil.rmtree(session_folder)
    else:
        print(f"Session folder not found: {session_folder}")

    # Also remove the original image file
    for filename in os.listdir(UPLOAD_FOLDER):
        if filename.startswith(f"{session_id}_"):
            file_path = os.path.join(UPLOAD_FOLDER, filename)
            print(f"Removing uploaded file: {file_path}")
            os.remove(file_path)

    return jsonify({'success': True})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080, debug=True)
