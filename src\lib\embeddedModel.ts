/**
 * Embedded Model
 *
 * This file contains the model configuration embedded directly in the code.
 * This ensures the model is always available without needing to load external files.
 */

import * as tf from '@tensorflow/tfjs';
import { toast } from 'sonner';

// Flag to track if the model is loaded
let modelLoaded = false;
// The loaded model instance
let loadedModel: tf.LayersModel | null = null;

/**
 * Create a model directly from the embedded configuration
 */
export async function createEmbeddedModel(): Promise<tf.LayersModel> {
  try {
    if (modelLoaded && loadedModel) {
      console.log('Using cached model');
      return loadedModel;
    }

    console.log('Creating embedded model...');

    // Make sure TensorFlow.js is ready
    await tf.ready();
    console.log('TensorFlow.js is ready');

    // Create a simpler model for compatibility
    // Use a smaller input size for better performance
    const input = tf.input({shape: [224, 224, 3]});

    // First convolutional layer
    const conv1 = tf.layers.conv2d({
      filters: 16,
      kernelSize: 3,
      strides: 2,
      padding: 'same',
      activation: 'relu'
    }).apply(input);

    // Max pooling
    const pool1 = tf.layers.maxPooling2d({
      poolSize: [2, 2]
    }).apply(conv1);

    // Second convolutional layer
    const conv2 = tf.layers.conv2d({
      filters: 32,
      kernelSize: 3,
      padding: 'same',
      activation: 'relu'
    }).apply(pool1);

    // Max pooling
    const pool2 = tf.layers.maxPooling2d({
      poolSize: [2, 2]
    }).apply(conv2);

    // Flatten
    const flatten = tf.layers.flatten().apply(pool2);

    // Dense layer
    const dense = tf.layers.dense({
      units: 64,
      activation: 'relu'
    }).apply(flatten);

    // Output layer - 3 values for stroma, tumor, vessel
    const output = tf.layers.dense({
      units: 3,
      activation: 'softmax'  // Ensures outputs sum to 1
    }).apply(dense);

    // Create the model
    const model = tf.model({
      inputs: input,
      outputs: output as tf.SymbolicTensor
    });

    // Compile the model
    model.compile({
      optimizer: 'adam',
      loss: 'categoricalCrossentropy',
      metrics: ['accuracy']
    });

    // Initialize the model with random weights
    // This is important to ensure the model is fully initialized
    const dummyInput = tf.zeros([1, 224, 224, 3]);
    const dummyOutput = model.predict(dummyInput) as tf.Tensor;
    dummyOutput.dataSync(); // Force execution of the prediction
    dummyOutput.dispose();
    dummyInput.dispose();

    console.log('Embedded model created and initialized successfully');

    // Cache the model
    loadedModel = model;
    modelLoaded = true;

    return model;
  } catch (error) {
    console.error('Error creating embedded model:', error);
    toast.error('Failed to create model');
    throw error;
  }
}

/**
 * Get the embedded model
 */
export async function getEmbeddedModel(): Promise<tf.LayersModel> {
  if (modelLoaded && loadedModel) {
    return loadedModel;
  }

  return createEmbeddedModel();
}
