export const theme = {
  colors: {
    primary: {
      // Cool medical blues
      DEFAULT: '#0284c7', // sky-600
      light: '#7dd3fc', // sky-300
      dark: '#075985', // sky-800
      accent: '#0ea5e9', // sky-500
    },
    secondary: {
      // Calming teals
      DEFAULT: '#0d9488',
      light: '#5eead4',
      dark: '#0f766e',
    },
    tertiary: {
      // Professional purples
      DEFAULT: '#7e22ce',
      light: '#c084fc',
      dark: '#6b21a8',
    },
    accent: {
      // Energetic accents
      teal: '#14b8a6',
      mint: '#10b981',
      azure: '#0ea5e9',
    },
    neutral: {
      white: '#ffffff',
      gray: {
        50: '#f8fafc',
        100: '#f1f5f9',
        200: '#e2e8f0',
        300: '#cbd5e1',
        400: '#94a3b8',
        500: '#64748b',
        600: '#475569',
        700: '#334155',
        800: '#1e293b',
        900: '#0f172a',
      },
    },
    state: {
      success: '#059669', // Emerald-600
      warning: '#eab308', // Yellow-500
      error: '#9333ea', // Purple-600 (avoiding red)
      info: '#0ea5e9', // Sky-500
    },
    medical: {
      stroma: '#4DA6FF', // Light blue
      vessel: '#4CAF50', // Green
      background: '#F0F7FF', // Light blue background
      highlight: '#38bdf8', // Sky-400
    }
  },
  fonts: {
    heading: '"Montserrat", sans-serif',
    body: '"Inter", "Open Sans", sans-serif',
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
  },
  borderRadius: {
    sm: '0.25rem',
    md: '0.5rem',
    lg: '1rem',
    full: '9999px',
  },
  shadows: {
    sm: '0 1px 2px rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px rgba(0, 0, 0, 0.07)',
    lg: '0 10px 15px rgba(0, 0, 0, 0.07)',
    highlight: '0 0 15px rgba(56, 189, 248, 0.3)', // Sky-400 glow
  },
  transitions: {
    default: '0.3s ease-in-out',
    fast: '0.15s ease-in-out',
    slow: '0.5s ease-in-out',
  },
  gradients: {
    primary: 'linear-gradient(to right, #0284c7, #0ea5e9)', // Sky gradient
    secondary: 'linear-gradient(to right, #0d9488, #14b8a6)', // Teal gradient
    accent: 'linear-gradient(to right, #7e22ce, #c084fc)', // Purple gradient
    medical: 'linear-gradient(to right, #0ea5e9, #7dd3fc)', // Medical blue gradient
    background: 'linear-gradient(to bottom right, #f0f9ff, #e0f2fe)', // Light blue background
  },
};
