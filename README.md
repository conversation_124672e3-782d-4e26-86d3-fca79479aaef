# Endometrium Patch Analyzer

This application analyzes histopathological patches from endometrium tissue samples, detecting tumor, stroma, and blood vessel regions.

## Features

- Upload and analyze individual tissue patches
- Upload and process whole slide images (WSI) to extract patches
- Analyze patches using TensorFlow.js model
- View analysis results with percentages for tumor, stroma, and blood vessels
- Compare patches with highest and lowest percentages for each tissue type
- Clinical matrix integration

## Prerequisites

- Node.js (v16 or higher)
- Python 3.8 or higher (for backend server)
- OpenSlide library (for whole slide image processing)

## Installation

### Frontend

1. Navigate to the project directory:
```
cd "C:\Users\<USER>\Downloads\endometrium-patch-analyzer-main (2)\endometrium-patch-analyzer-main (2)\endometrium-patch-analyzer-main\endometrium-patch-analyzer-main"
```

2. Install dependencies:
```
npm install
```

3. Install TensorFlow.js:
```
npm install @tensorflow/tfjs@3.21.0
```

4. Update browserslist database:
```
npm update caniuse-lite
```

### Backend (for Whole Slide Image Processing)

1. Navigate to the backend directory:
```
cd "C:\Users\<USER>\Downloads\endometrium-patch-analyzer-main (2)\endometrium-patch-analyzer-main (2)\endometrium-patch-analyzer-main\endometrium-patch-analyzer-main\backend"
```

2. Create a virtual environment (optional but recommended):
```
python -m venv venv
```

3. Activate the virtual environment:
- Windows:
```
venv\Scripts\activate
```
- Linux/Mac:
```
source venv/bin/activate
```

4. Install dependencies:
```
pip install -r requirements.txt
```

5. Install OpenSlide:
- Windows: Download from https://openslide.org/download/ and add to PATH
- Linux: `sudo apt-get install openslide-tools`
- Mac: `brew install openslide`

## Running the Application

### Start the Backend Server (for Whole Slide Image Processing)

1. Navigate to the backend directory:
```
cd "C:\Users\<USER>\Downloads\endometrium-patch-analyzer-main (2)\endometrium-patch-analyzer-main (2)\endometrium-patch-analyzer-main\endometrium-patch-analyzer-main\backend"
```

2. Activate the virtual environment (if you created one):
- Windows:
```
venv\Scripts\activate
```
- Linux/Mac:
```
source venv/bin/activate
```

3. Start the backend server:
```
python app.py
```

The backend server will run on http://localhost:8080.

### Start the Frontend Server

1. Navigate to the project directory:
```
cd "C:\Users\<USER>\Downloads\endometrium-patch-analyzer-main (2)\endometrium-patch-analyzer-main (2)\endometrium-patch-analyzer-main\endometrium-patch-analyzer-main"
```

2. Start the development server:
```
npx vite
```

The frontend will be available at http://localhost:5174 (or another port if 5174 is in use).

## Usage

### Analyzing Individual Patches

1. Click on the "Upload Patches" tab
2. Click "Upload Folder" to select image files
3. Click "Analyze Patches" to process the images
4. View the results with percentages for tumor, stroma, and blood vessels
5. Use the "Highest/Lowest" tab to see patches with extreme values

### Analyzing Whole Slide Images

1. Click on the "Whole Slide Image" tab
2. Click to select a whole slide image file (.svs, .tif, .tiff, .ndpi, .mrxs)
3. Click "Upload & Process" to upload and process the image
4. Wait for the patches to be extracted and downloaded
5. Once complete, the patches will be loaded into the application
6. Click "Analyze Patches" to process the extracted patches
7. View the results as with individual patches

## Troubleshooting

- If you encounter issues with TensorFlow.js, try installing a specific version:
  ```
  npm install @tensorflow/tfjs@3.21.0
  ```

- If OpenSlide is not found, make sure it's properly installed and in your PATH.

- For backend server issues, check that all dependencies are installed:
  ```
  pip install -r requirements.txt
  ```
