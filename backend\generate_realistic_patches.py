import os
import numpy as np
import cv2
from PIL import Image
import time
import random
import shutil

# Create output directory
output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'sample_patches')
os.makedirs(output_dir, exist_ok=True)
print(f"Output directory created at: {output_dir}")

# Function to create sample patches with different tissue patterns
def create_sample_tissue_patch(width, height, patch_type):
    """
    Create a sample tissue patch with realistic appearance
    patch_type: 'tumor', 'stroma', 'vessel', or 'mixed'
    """
    # Create a blank canvas
    patch = np.ones((height, width, 3), dtype=np.uint8) * 255
    
    if patch_type == 'tumor':
        # Create tumor-like pattern (reddish-purple cells)
        base_color = np.array([180, 120, 180], dtype=np.uint8)  # Reddish-purple
        for _ in range(1000):
            x = random.randint(0, width-1)
            y = random.randint(0, height-1)
            radius = random.randint(3, 8)
            color = base_color + np.random.randint(-20, 20, 3)
            cv2.circle(patch, (x, y), radius, color.tolist(), -1)
            
    elif patch_type == 'stroma':
        # Create stroma-like pattern (pink fibrous texture)
        base_color = np.array([200, 150, 200], dtype=np.uint8)  # Pink
        for _ in range(200):
            x1 = random.randint(0, width-1)
            y1 = random.randint(0, height-1)
            x2 = x1 + random.randint(-100, 100)
            y2 = y1 + random.randint(-100, 100)
            thickness = random.randint(1, 3)
            color = base_color + np.random.randint(-20, 20, 3)
            cv2.line(patch, (x1, y1), (x2, y2), color.tolist(), thickness)
            
    elif patch_type == 'vessel':
        # Create vessel-like pattern (red circular structures)
        base_color = np.array([100, 100, 200], dtype=np.uint8)  # Red
        for _ in range(5):
            x = random.randint(50, width-50)
            y = random.randint(50, height-50)
            radius = random.randint(15, 30)
            thickness = random.randint(2, 5)
            color = base_color + np.random.randint(-20, 20, 3)
            cv2.circle(patch, (x, y), radius, color.tolist(), thickness)
            
    elif patch_type == 'mixed':
        # Create mixed tissue pattern
        # First add stroma background
        base_color = np.array([220, 180, 220], dtype=np.uint8)  # Light pink
        for _ in range(100):
            x1 = random.randint(0, width-1)
            y1 = random.randint(0, height-1)
            x2 = x1 + random.randint(-100, 100)
            y2 = y1 + random.randint(-100, 100)
            thickness = random.randint(1, 3)
            color = base_color + np.random.randint(-20, 20, 3)
            cv2.line(patch, (x1, y1), (x2, y2), color.tolist(), thickness)
            
        # Add some tumor cells
        tumor_color = np.array([180, 120, 180], dtype=np.uint8)
        for _ in range(500):
            x = random.randint(0, width-1)
            y = random.randint(0, height-1)
            radius = random.randint(3, 6)
            color = tumor_color + np.random.randint(-20, 20, 3)
            cv2.circle(patch, (x, y), radius, color.tolist(), -1)
    
    # Add some texture and noise
    noise = np.random.randint(0, 15, patch.shape, dtype=np.uint8)
    patch = cv2.add(patch, noise)
    
    # Convert to PIL Image
    return Image.fromarray(patch)

# Generate sample patches
def generate_sample_patches(num_patches=20):
    """Generate sample tissue patches and save them to the output directory"""
    patch_types = ['tumor', 'stroma', 'vessel', 'mixed']
    patch_width, patch_height = 1080, 566  # Same dimensions as in the original code
    
    patches_info = []
    
    # First, clean up any existing tissue_patch files
    for f in os.listdir(output_dir):
        if f.startswith('tissue_patch_') and f.endswith('.png'):
            os.remove(os.path.join(output_dir, f))
    
    for i in range(num_patches):
        # Select a random patch type
        patch_type = random.choice(patch_types)
        
        # Create the patch
        patch = create_sample_tissue_patch(patch_width, patch_height, patch_type)
        
        # Save the patch
        patch_filename = f"tissue_patch_{i}.png"
        patch_path = os.path.join(output_dir, patch_filename)
        patch.save(patch_path)
        
        print(f"Saved {patch_type} patch {i} to {patch_path}")
        
        # Add patch info
        patches_info.append({
            'id': i,
            'filename': patch_filename,
            'x': i * patch_width,  # Dummy coordinates
            'y': 0,
            'width': patch_width,
            'height': patch_height,
            'patchId': f"patch_{i}.png",
            'type': patch_type
        })
    
    return patches_info

# Generate the patches
start_time = time.time()
patches_info = generate_sample_patches(20)
end_time = time.time()

print(f"Generated {len(patches_info)} sample patches")
print(f"Generation time: {end_time - start_time:.2f} seconds")

# Write patch info to a file for reference
import json
with open(os.path.join(output_dir, 'patches_info.json'), 'w') as f:
    json.dump(patches_info, f, indent=2)

print("Sample patch generation complete!")
