import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Card, CardContent } from './ui/card';
import {
  MicroscopeIcon,
  ShieldCheckIcon,
  ZapIcon,
  TrendingUpIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  PlayIcon,
  PauseIcon
} from 'lucide-react';

const heroSlides = [
	{
		image: '/slideshow/medical-ai-analysis.svg',
		title: 'Revolutionary AI-Powered Detection',
		subtitle: 'Endometrial Cancer Analysis',
		description: 'Advanced machine learning algorithms analyze tissue patterns with unprecedented accuracy, enabling early detection when treatment is most effective.',
		stats: { accuracy: '95%', speed: '< 2 min', reliability: '99.2%' },
		gradient: 'from-sky-400 via-blue-500 to-indigo-600'
	},
	{
		image: '/slideshow/non-invasive-technology.svg',
		title: 'Non-Invasive Precision Medicine',
		subtitle: 'Comfortable & Accurate',
		description: 'State-of-the-art patch technology provides comprehensive analysis without discomfort, making regular screening accessible to all women.',
		stats: { comfort: '100%', accuracy: '94%', time: '5 min' },
		gradient: 'from-emerald-400 via-teal-500 to-cyan-600'
	},
	{
		image: '/slideshow/instant-results.svg',
		title: 'Instant Clinical Insights',
		subtitle: 'Real-Time Analysis',
		description: 'Get immediate, detailed tissue composition analysis with clinical-grade accuracy, empowering healthcare providers with actionable insights.',
		stats: { speed: 'Real-time', precision: '96%', coverage: '100%' },
		gradient: 'from-purple-400 via-violet-500 to-indigo-600'
	},
	{
		image: '/slideshow/global-impact.svg',
		title: 'Global Healthcare Impact',
		subtitle: 'Saving Lives Worldwide',
		description: 'Our technology is making early detection accessible globally, with proven results in reducing mortality rates through timely intervention.',
		stats: { lives: '10K+', countries: '25+', success: '98%' },
		gradient: 'from-orange-400 via-amber-500 to-yellow-600'
	}
];

export const Hero: React.FC = () => {
	const [currentSlide, setCurrentSlide] = useState(0);
	const [isPlaying, setIsPlaying] = useState(true);

	useEffect(() => {
		if (!isPlaying) return;

		const interval = setInterval(() => {
			setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
		}, 5000);

		return () => clearInterval(interval);
	}, [isPlaying]);

	const nextSlide = () => {
		setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
	};

	const prevSlide = () => {
		setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length);
	};

	const currentSlideData = heroSlides[currentSlide];

	return (
		<section className="relative min-h-screen overflow-hidden">
			{/* Dynamic Background */}
			<div className={`absolute inset-0 bg-gradient-to-br ${currentSlideData.gradient} opacity-10 transition-all duration-1000`} />

			{/* Medical Pattern Overlay */}
			<div
				className="absolute inset-0 opacity-5"
				style={{
					backgroundImage: `url('/medical-backgrounds/medical-pattern.svg')`,
					backgroundSize: '200px 200px',
					backgroundRepeat: 'repeat'
				}}
			/>

			{/* Floating Medical Elements */}
			<div className="absolute inset-0 overflow-hidden">
				{[...Array(6)].map((_, i) => (
					<motion.div
						key={i}
						className="absolute w-20 h-20 opacity-5"
						initial={{
							x: Math.random() * window.innerWidth,
							y: Math.random() * window.innerHeight,
							rotate: 0
						}}
						animate={{
							y: [null, -20, 0],
							rotate: [0, 360],
							scale: [1, 1.1, 1]
						}}
						transition={{
							duration: 10 + i * 2,
							repeat: Infinity,
							ease: "linear"
						}}
					>
						<MicroscopeIcon className="w-full h-full text-sky-500" />
					</motion.div>
				))}
			</div>

			<div className="container mx-auto px-4 py-20 relative z-10">
				<div className="grid lg:grid-cols-2 gap-16 items-center min-h-[80vh]">
					{/* Content Side */}
					<motion.div
						className="space-y-8 text-center lg:text-left"
						initial={{ opacity: 0, x: -50 }}
						animate={{ opacity: 1, x: 0 }}
						transition={{ duration: 0.8 }}
					>
						<div className="space-y-4">
							<motion.div
								key={currentSlide}
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								transition={{ duration: 0.6 }}
							>
								<Badge
									className={`mb-4 bg-gradient-to-r ${currentSlideData.gradient} text-white border-0 px-4 py-2`}
								>
									{currentSlideData.subtitle}
								</Badge>
								<h1 className="text-5xl md:text-6xl font-bold text-primary-navy leading-tight">
									{currentSlideData.title}
								</h1>
							</motion.div>

							<motion.p
								className="text-xl text-medium-gray max-w-2xl mx-auto lg:mx-0 leading-relaxed"
								key={`desc-${currentSlide}`}
								initial={{ opacity: 0 }}
								animate={{ opacity: 1 }}
								transition={{ delay: 0.3, duration: 0.6 }}
							>
								{currentSlideData.description}
							</motion.p>
						</div>

						{/* Statistics */}
						<motion.div
							className="grid grid-cols-3 gap-4 max-w-md mx-auto lg:mx-0"
							key={`stats-${currentSlide}`}
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ delay: 0.5, duration: 0.6 }}
						>
							{Object.entries(currentSlideData.stats).map(([key, value], index) => (
								<div key={key} className="text-center">
									<div className="text-2xl font-bold text-primary-navy">{value}</div>
									<div className="text-sm text-medium-gray capitalize">{key}</div>
								</div>
							))}
						</motion.div>

						{/* Action Buttons */}
						<motion.div
							className="flex flex-wrap gap-4 justify-center lg:justify-start"
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ delay: 0.7, duration: 0.6 }}
						>
							<Button
								size="lg"
								className={`bg-gradient-to-r ${currentSlideData.gradient} hover:opacity-90 text-white border-0 px-8 py-3 text-lg shadow-lg hover:shadow-xl transition-all duration-300`}
							>
								<ZapIcon className="w-5 h-5 mr-2" />
								Start Analysis
							</Button>
							<Button
								size="lg"
								variant="outline"
								className="border-2 border-primary-navy text-primary-navy hover:bg-primary-navy hover:text-white px-8 py-3 text-lg transition-all duration-300"
							>
								<ShieldCheckIcon className="w-5 h-5 mr-2" />
								Learn More
							</Button>
						</motion.div>
					</motion.div>

					{/* Slideshow Side */}
					<motion.div
						className="relative"
						initial={{ opacity: 0, x: 50 }}
						animate={{ opacity: 1, x: 0 }}
						transition={{ duration: 0.8, delay: 0.2 }}
					>
						<Card className="relative overflow-hidden border-0 shadow-2xl bg-white/95 backdrop-blur-sm">
							<div className="relative h-96 md:h-[500px]">
								<AnimatePresence mode="wait">
									<motion.div
										key={currentSlide}
										initial={{ opacity: 0, scale: 1.1 }}
										animate={{ opacity: 1, scale: 1 }}
										exit={{ opacity: 0, scale: 0.9 }}
										transition={{ duration: 0.7 }}
										className="absolute inset-0"
									>
										<img
											src={currentSlideData.image}
											alt={currentSlideData.title}
											className="w-full h-full object-cover"
										/>
										<div className={`absolute inset-0 bg-gradient-to-t ${currentSlideData.gradient} opacity-20`} />
									</motion.div>
								</AnimatePresence>

								{/* Slide Controls */}
								<div className="absolute bottom-4 left-4 right-4 flex items-center justify-between">
									<div className="flex gap-2">
										{heroSlides.map((_, index) => (
											<button
												key={index}
												onClick={() => setCurrentSlide(index)}
												className={`w-3 h-3 rounded-full transition-all duration-300 ${
													index === currentSlide
														? 'bg-white shadow-lg scale-125'
														: 'bg-white/50 hover:bg-white/75'
												}`}
											/>
										))}
									</div>

									<div className="flex gap-2">
										<button
											onClick={prevSlide}
											className="p-2 bg-white/20 backdrop-blur-sm rounded-full hover:bg-white/30 transition-all duration-300"
										>
											<ChevronLeftIcon className="w-4 h-4 text-white" />
										</button>
										<button
											onClick={() => setIsPlaying(!isPlaying)}
											className="p-2 bg-white/20 backdrop-blur-sm rounded-full hover:bg-white/30 transition-all duration-300"
										>
											{isPlaying ?
												<PauseIcon className="w-4 h-4 text-white" /> :
												<PlayIcon className="w-4 h-4 text-white" />
											}
										</button>
										<button
											onClick={nextSlide}
											className="p-2 bg-white/20 backdrop-blur-sm rounded-full hover:bg-white/30 transition-all duration-300"
										>
											<ChevronRightIcon className="w-4 h-4 text-white" />
										</button>
									</div>
								</div>
							</div>
						</Card>

						{/* Floating Stats Card */}
						<motion.div
							className="absolute -bottom-8 -left-8 bg-white rounded-xl shadow-xl p-6 border-l-4 border-sky-500"
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ delay: 1, duration: 0.6 }}
						>
							<div className="flex items-center gap-3">
								<div className="p-2 bg-sky-100 rounded-lg">
									<TrendingUpIcon className="w-6 h-6 text-sky-600" />
								</div>
								<div>
									<div className="text-2xl font-bold text-primary-navy">95%</div>
									<div className="text-sm text-medium-gray">Early Detection Rate</div>
								</div>
							</div>
						</motion.div>
					</motion.div>
				</div>

				{/* Key Features */}
				<motion.div
					className="grid md:grid-cols-3 gap-8 mt-24"
					initial={{ opacity: 0, y: 50 }}
					whileInView={{ opacity: 1, y: 0 }}
					viewport={{ once: true }}
					transition={{ duration: 0.8 }}
				>
					{[
						{
							title: 'AI-Powered Analysis',
							description: 'Advanced machine learning algorithms provide unprecedented accuracy in tissue analysis',
							icon: <MicroscopeIcon className="w-8 h-8" />,
							gradient: 'from-blue-500 to-indigo-600'
						},
						{
							title: 'Non-Invasive Technology',
							description: 'Comfortable patch-based screening that makes regular monitoring accessible',
							icon: <ShieldCheckIcon className="w-8 h-8" />,
							gradient: 'from-emerald-500 to-teal-600'
						},
						{
							title: 'Instant Results',
							description: 'Real-time analysis with clinical-grade accuracy for immediate insights',
							icon: <ZapIcon className="w-8 h-8" />,
							gradient: 'from-purple-500 to-violet-600'
						},
					].map((feature, index) => (
						<motion.div
							key={index}
							initial={{ opacity: 0, y: 20 }}
							whileInView={{ opacity: 1, y: 0 }}
							viewport={{ once: true }}
							transition={{ delay: index * 0.2, duration: 0.6 }}
						>
							<Card className="relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 group cursor-pointer">
								<div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-5 group-hover:opacity-10 transition-opacity duration-300`} />
								<CardContent className="p-8 text-center relative z-10">
									<div className={`inline-flex p-4 rounded-xl bg-gradient-to-br ${feature.gradient} text-white mb-6 group-hover:scale-110 transition-transform duration-300`}>
										{feature.icon}
									</div>
									<h3 className="text-xl font-semibold text-primary-navy mb-4">
										{feature.title}
									</h3>
									<p className="text-medium-gray leading-relaxed">
										{feature.description}
									</p>
								</CardContent>
							</Card>
						</motion.div>
					))}
				</motion.div>
			</div>
		</section>
	);
};
