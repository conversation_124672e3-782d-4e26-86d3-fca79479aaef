/**
 * Direct Model Loader
 *
 * This utility directly loads the TensorFlow.js model using a hardcoded path
 * to ensure it works regardless of the server configuration.
 */

import * as tf from '@tensorflow/tfjs';
import { toast } from 'sonner';

// The model configuration from the model.json file
const MODEL_CONFIG = {
  "format": "layers-model",
  "generatedBy": "keras v3.8.0",
  "convertedBy": "TensorFlow.js Converter v4.22.0",
  "modelTopology": {
    "keras_version": "3.8.0",
    "backend": "tensorflow",
    "model_config": {
      "class_name": "Functional",
      "config": {
        "name": "functional_9",
        "trainable": true,
        "layers": [
          {
            "class_name": "InputLayer",
            "config": {
              "batch_shape": [null, 560, 1080, 3],
              "dtype": "float32",
              "sparse": false,
              "name": "input_layer_9"
            },
            "name": "input_layer_9",
            "inbound_nodes": []
          },
          // ... (truncated for brevity)
        ],
        "input_layers": [["input_layer_9", 0, 0]],
        "output_layers": [["dense_9", 0, 0]]
      }
    },
    "training_config": {
      "loss": "mean_squared_error",
      "loss_weights": null,
      "metrics": [
        {
          "class_name": "MeanSquaredError",
          "config": {
            "name": "mean_squared_error",
            "dtype": "float32"
          }
        },
        {
          "class_name": "MeanAbsoluteError",
          "config": {
            "name": "mean_absolute_error",
            "dtype": "float32"
          }
        },
        "r_squared"
      ],
      "weighted_metrics": null,
      "run_eagerly": false,
      "steps_per_execution": 1,
      "jit_compile": true,
      "optimizer_config": {
        "class_name": "Adam",
        "config": {
          "name": "adam",
          "learning_rate": 0.0010000000474974513,
          "weight_decay": null,
          "clipnorm": null,
          "global_clipnorm": null,
          "clipvalue": null,
          "use_ema": false,
          "ema_momentum": 0.99,
          "ema_overwrite_frequency": null,
          "loss_scale_factor": null,
          "gradient_accumulation_steps": null,
          "beta_1": 0.9,
          "beta_2": 0.999,
          "epsilon": 1e-07,
          "amsgrad": false
        }
      }
    }
  },
  "weightsManifest": [
    {
      "paths": ["group1-shard1of1.bin"],
      "weights": [
        {
          "name": "batch_normalization_27/gamma",
          "shape": [32],
          "dtype": "float32"
        },
        {
          "name": "batch_normalization_27/beta",
          "shape": [32],
          "dtype": "float32"
        },
        // ... (truncated for brevity)
      ]
    }
  ]
};

/**
 * Directly load the TensorFlow.js model using a hardcoded path
 */
export async function loadModelDirectly(): Promise<tf.LayersModel | null> {
  try {
    // First, log the current URL to help with debugging
    console.log('Current URL:', window.location.href);
    console.log('Origin:', window.location.origin);

    // Try to fetch the model.json file directly to check if it exists
    try {
      const response = await fetch('/models/tfjs_model/model.json');
      if (response.ok) {
        console.log('model.json exists and is accessible!');
        const modelJson = await response.json();
        console.log('Model JSON structure:', modelJson);
      } else {
        console.error('model.json fetch failed with status:', response.status);
      }
    } catch (fetchError) {
      console.error('Error fetching model.json:', fetchError);
    }

    // Try multiple paths to find the model
    const possiblePaths = [
      'models/tfjs_model/model.json',
      '/models/tfjs_model/model.json',
      './models/tfjs_model/model.json',
      '../models/tfjs_model/model.json',
      window.location.origin + '/models/tfjs_model/model.json'
    ];

    // Try each path with detailed error logging
    for (const path of possiblePaths) {
      try {
        console.log(`Attempting to load model from ${path}`);

        // Use a more robust approach with explicit error handling
        const model = await tf.loadLayersModel(path);

        // If we get here, the model loaded successfully
        console.log(`Successfully loaded model from ${path}`);
        console.log('Model summary:', model.summary());
        return model;
      } catch (error) {
        console.error(`Failed to load model from ${path}:`, error);
        // Log more details about the error
        if (error instanceof Error) {
          console.error('Error name:', error.name);
          console.error('Error message:', error.message);
          console.error('Error stack:', error.stack);
        }
      }
    }

    // If all paths fail, try a different approach with fetch first
    try {
      console.log('Trying alternative approach with fetch first...');

      // First fetch the model.json to verify it exists and is valid
      const modelJsonResponse = await fetch('/models/tfjs_model/model.json');
      if (!modelJsonResponse.ok) {
        throw new Error(`Failed to fetch model.json: ${modelJsonResponse.status}`);
      }

      const modelJson = await modelJsonResponse.json();
      console.log('Successfully fetched model.json:', modelJson);

      // Now try to load the model with the verified path
      const model = await tf.loadLayersModel('/models/tfjs_model/model.json');
      console.log('Successfully loaded model with alternative approach');
      return model;
    } catch (error) {
      console.error('Alternative approach failed:', error);
    }

    // If all else fails, try to create a model from the hardcoded config
    try {
      console.log('Attempting to create model from hardcoded config');

      // First, register the model in TensorFlow.js model store
      await tf.io.registerLoadRouter((url) => {
        if (url === 'hardcoded-model://tfjs_model') {
          return {
            load: async () => {
              return {
                modelTopology: MODEL_CONFIG.modelTopology,
                weightSpecs: MODEL_CONFIG.weightsManifest[0].weights,
                weightData: new ArrayBuffer(0) // This won't work without actual weights
              };
            }
          };
        }
        throw new Error(`Unknown URL scheme: ${url}`);
      });

      // This won't actually work without the weights, but we're trying everything
      const model = await tf.loadLayersModel('hardcoded-model://tfjs_model');
      console.log('Successfully created model from hardcoded config');
      return model;
    } catch (error) {
      console.log(`Failed to create model from hardcoded config: ${error}`);
    }

    // If all attempts fail, return null
    console.error('All attempts to load the model failed');
    return null;
  } catch (error) {
    console.error('Error in loadModelDirectly:', error);
    return null;
  }
}

/**
 * Create a simple model for testing
 * This is a last resort if we can't load the real model
 */
export function createSimpleModel(): tf.LayersModel {
  // Create a simple model that takes an image and outputs 3 values
  const input = tf.input({shape: [224, 224, 3]});
  const conv1 = tf.layers.conv2d({
    filters: 16,
    kernelSize: 3,
    strides: 2,
    padding: 'same',
    activation: 'relu'
  }).apply(input);

  const flatten = tf.layers.flatten().apply(conv1);

  const dense = tf.layers.dense({
    units: 3,
    activation: 'softmax'
  }).apply(flatten);

  const model = tf.model({inputs: input, outputs: dense as tf.SymbolicTensor});

  // Compile the model
  model.compile({
    optimizer: 'adam',
    loss: 'meanSquaredError',
    metrics: ['accuracy']
  });

  console.log('Created simple test model');
  return model;
}
