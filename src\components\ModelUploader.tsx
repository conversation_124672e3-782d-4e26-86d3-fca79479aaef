import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { downloadModelFromDrive, hasCustomModel } from '@/lib/modelDownloader';

interface ModelUploaderProps {
  onModelUploaded: () => void;
}

const ModelUploader: React.FC<ModelUploaderProps> = ({ onModelUploaded }) => {
  const [driveLink, setDriveLink] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [hasModel, setHasModel] = useState(hasCustomModel());

  const handleDriveLinkChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDriveLink(e.target.value);
  };

  const handleUpload = async () => {
    if (!driveLink) {
      toast.error('Please enter a Google Drive link');
      return;
    }

    // Validate the link format
    if (!driveLink.includes('drive.google.com')) {
      toast.error('Please enter a valid Google Drive link');
      return;
    }

    setIsUploading(true);
    try {
      const success = await downloadModelFromDrive(driveLink);
      if (success) {
        toast.success('Model uploaded successfully');
        setHasModel(true);
        onModelUploaded();
      } else {
        toast.error('Failed to upload model');
      }
    } catch (error) {
      console.error('Error uploading model:', error);
      toast.error('An error occurred while uploading the model');
    } finally {
      setIsUploading(false);
    }
  };

  const handleReset = () => {
    // Clear the custom model from local storage
    localStorage.removeItem('customModelUrl');
    localStorage.removeItem('usingCustomModel');
    setHasModel(false);
    toast.success('Custom model removed. Using default model.');
    onModelUploaded();
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Model Configuration</CardTitle>
        <CardDescription>
          {hasModel
            ? 'A custom model is currently loaded. You can reset to use the default model.'
            : 'Upload a custom model from Google Drive to use for analysis.'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {!hasModel ? (
          <div className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="drive-link" className="text-sm font-medium">
                Google Drive Link
              </label>
              <Input
                id="drive-link"
                placeholder="https://drive.google.com/file/d/..."
                value={driveLink}
                onChange={handleDriveLinkChange}
                disabled={isUploading}
              />
              <p className="text-xs text-muted-foreground">
                Paste a Google Drive link to your pretrained model file (.h5 or TensorFlow.js format)
              </p>
            </div>
          </div>
        ) : (
          <div className="text-sm">
            <p className="font-medium text-green-600">Custom model is active</p>
            <p className="text-muted-foreground mt-1">
              The custom model from Google Drive will be used for all analyses.
            </p>
            <p className="text-amber-600 mt-2 text-xs">
              Note: Since .h5 files can't be directly used in browsers, a simulation mode is used with parameters derived from your model.
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              Model ID: {localStorage.getItem('customModelId') || 'Unknown'}
            </p>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-end space-x-2">
        {hasModel ? (
          <Button variant="destructive" onClick={handleReset} disabled={isUploading}>
            Reset to Default Model
          </Button>
        ) : (
          <Button onClick={handleUpload} disabled={isUploading}>
            {isUploading ? 'Uploading...' : 'Upload Model'}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default ModelUploader;
