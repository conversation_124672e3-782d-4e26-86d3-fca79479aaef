import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3Icon, 
  TrendingUpIcon, 
  TargetIcon,
  ActivityIcon,
  CheckCircleIcon,
  StarIcon,
  AwardIcon,
  ZapIcon
} from 'lucide-react';

const ModelPerformanceSection: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const performanceMetrics = [
    {
      title: "Overall Accuracy",
      value: "96.2%",
      subtitle: "Classification Performance",
      description: "Exceptional accuracy across all tissue types",
      icon: <TargetIcon className="w-6 h-6" />,
      color: "from-emerald-500 to-teal-600",
      improvement: "+2.1%"
    },
    {
      title: "Tumor Detection",
      value: "95%",
      subtitle: "AUC Score",
      description: "Outstanding tumor identification capability",
      icon: <ActivityIcon className="w-6 h-6" />,
      color: "from-sky-500 to-blue-600",
      improvement: "+1.8%"
    },
    {
      title: "Stroma Analysis",
      value: "100%",
      subtitle: "Perfect AUC",
      description: "Flawless stroma tissue classification",
      icon: <StarIcon className="w-6 h-6" />,
      color: "from-purple-500 to-indigo-600",
      improvement: "Perfect"
    },
    {
      title: "Vessel Detection",
      value: "96%",
      subtitle: "AUC Score",
      description: "Highly accurate vessel identification",
      icon: <ZapIcon className="w-6 h-6" />,
      color: "from-orange-500 to-amber-600",
      improvement: "+3.2%"
    }
  ];

  const modelCharts = [
    {
      id: 'confusion-matrix',
      title: 'Confusion Matrix',
      description: 'Detailed breakdown of classification accuracy across all tissue types',
      image: '/charts/confusion-matrix.jpeg',
      insights: [
        'Tumor detection: 532 correct predictions with only 3 false negatives',
        'Stroma classification: Perfect 18/18 accuracy (100%)',
        'Vessel identification: 19 samples correctly classified',
        'Minimal false positives across all tissue types'
      ]
    },
    {
      id: 'roc-curves',
      title: 'ROC Curves',
      description: 'Receiver Operating Characteristic curves showing exceptional model performance',
      image: '/charts/roc-curves.jpeg',
      insights: [
        'Stroma: Perfect AUC = 1.00 (100% discrimination)',
        'Vessel: Excellent AUC = 0.96 (96% discrimination)',
        'Tumor: Outstanding AUC = 0.95 (95% discrimination)',
        'All curves demonstrate superior performance vs random classifier'
      ]
    },
    {
      id: 'training-metrics',
      title: 'Training Metrics',
      description: 'Comprehensive training performance and convergence analysis',
      image: '/charts/training-metrics.jpeg',
      insights: [
        'Rapid loss convergence within first 5 epochs',
        'Stable R² score plateau at ~0.82 indicating good fit',
        'Classification metrics (accuracy, precision, recall) >96%',
        'Minimal overfitting with stable validation performance'
      ]
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div 
        className="absolute inset-0 opacity-5"
        style={{
          backgroundImage: `url('/medical-backgrounds/microscope-pattern.svg')`,
          backgroundSize: '150px 150px',
          backgroundRepeat: 'repeat'
        }}
      />
      
      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <Badge className="mb-4 bg-gradient-to-r from-purple-500 to-indigo-600 text-white border-0 px-6 py-2">
            <AwardIcon className="w-4 h-4 mr-2" />
            Model Performance Excellence
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-primary-navy mb-6">
            Exceptional AI Performance
          </h2>
          <p className="text-xl text-medium-gray max-w-3xl mx-auto leading-relaxed">
            Our TensorFlow.js model demonstrates outstanding performance across all tissue classification tasks, 
            achieving state-of-the-art accuracy in endometrial cancer detection.
          </p>
        </motion.div>

        {/* Performance Metrics Cards */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {performanceMetrics.map((metric, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <Card className="relative overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-300 group">
                <div className={`absolute inset-0 bg-gradient-to-br ${metric.color} opacity-5 group-hover:opacity-10 transition-opacity duration-300`} />
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between mb-3">
                    <div className={`p-3 rounded-xl bg-gradient-to-br ${metric.color} text-white shadow-lg`}>
                      {metric.icon}
                    </div>
                    <Badge 
                      variant="secondary"
                      className="bg-emerald-100 text-emerald-700 text-xs"
                    >
                      {metric.improvement}
                    </Badge>
                  </div>
                  <CardTitle className="text-3xl font-bold text-primary-navy">
                    {metric.value}
                  </CardTitle>
                  <CardDescription className="text-sm font-medium text-cerulean">
                    {metric.subtitle}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-medium-gray leading-relaxed">
                    {metric.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Model Performance Charts */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="max-w-7xl mx-auto"
        >
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-8 bg-white/80 backdrop-blur-sm shadow-lg">
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <BarChart3Icon className="w-4 h-4" />
                Confusion Matrix
              </TabsTrigger>
              <TabsTrigger value="roc" className="flex items-center gap-2">
                <TrendingUpIcon className="w-4 h-4" />
                ROC Curves
              </TabsTrigger>
              <TabsTrigger value="training" className="flex items-center gap-2">
                <ActivityIcon className="w-4 h-4" />
                Training Metrics
              </TabsTrigger>
            </TabsList>

            {modelCharts.map((chart, index) => (
              <TabsContent 
                key={chart.id} 
                value={index === 0 ? 'overview' : index === 1 ? 'roc' : 'training'} 
                className="space-y-8"
              >
                <Card className="border-0 shadow-xl bg-white/95 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-primary-navy text-2xl">
                      <BarChart3Icon className="w-6 h-6" />
                      {chart.title}
                    </CardTitle>
                    <CardDescription className="text-lg">
                      {chart.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid lg:grid-cols-2 gap-8">
                      {/* Chart Image */}
                      <div className="bg-white rounded-xl p-6 shadow-lg">
                        <img
                          src={chart.image}
                          alt={chart.title}
                          className="w-full h-auto rounded-lg"
                          style={{ maxHeight: '400px', objectFit: 'contain' }}
                          onError={(e) => {
                            console.log(`Failed to load image: ${chart.image}`);
                            e.currentTarget.style.display = 'none';
                          }}
                          onLoad={() => {
                            console.log(`Successfully loaded: ${chart.image}`);
                          }}
                        />
                      </div>
                      
                      {/* Insights */}
                      <div className="space-y-6">
                        <h4 className="text-xl font-semibold text-primary-navy">Key Insights</h4>
                        <div className="space-y-4">
                          {chart.insights.map((insight, idx) => (
                            <motion.div
                              key={idx}
                              initial={{ opacity: 0, x: -20 }}
                              whileInView={{ opacity: 1, x: 0 }}
                              viewport={{ once: true }}
                              transition={{ delay: idx * 0.1 }}
                              className="flex items-start gap-3 p-4 bg-sky-50 rounded-lg border-l-4 border-sky-500"
                            >
                              <CheckCircleIcon className="w-5 h-5 text-sky-600 mt-0.5 flex-shrink-0" />
                              <p className="text-medium-gray leading-relaxed">{insight}</p>
                            </motion.div>
                          ))}
                        </div>
                        
                        {/* Performance Summary */}
                        <div className="mt-6 p-6 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl border border-emerald-200">
                          <h5 className="font-semibold text-emerald-800 mb-2">Performance Summary</h5>
                          <p className="text-emerald-700 text-sm">
                            {index === 0 && "Exceptional classification accuracy with minimal false positives across all tissue types."}
                            {index === 1 && "Outstanding ROC performance demonstrates superior discriminative ability for all tissue classes."}
                            {index === 2 && "Rapid convergence and stable performance indicate robust model architecture and training."}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          </Tabs>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mt-16"
        >
          <Card className="border-0 shadow-xl bg-gradient-to-r from-purple-500 to-indigo-600 text-white max-w-4xl mx-auto">
            <CardContent className="p-12">
              <h3 className="text-3xl font-bold mb-4">
                State-of-the-Art Performance
              </h3>
              <p className="text-xl mb-8 text-purple-100 leading-relaxed">
                Our model achieves exceptional performance metrics that exceed industry standards, 
                providing reliable and accurate endometrial cancer detection for clinical use.
              </p>
              <div className="flex flex-wrap gap-4 justify-center">
                <Badge className="bg-white/20 text-white border-white/30 px-6 py-3 text-lg">
                  <CheckCircleIcon className="w-5 h-5 mr-2" />
                  96.2% Overall Accuracy
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30 px-6 py-3 text-lg">
                  <StarIcon className="w-5 h-5 mr-2" />
                  Perfect Stroma Detection
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30 px-6 py-3 text-lg">
                  <AwardIcon className="w-5 h-5 mr-2" />
                  Clinical Grade Results
                </Badge>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
};

export default ModelPerformanceSection;
