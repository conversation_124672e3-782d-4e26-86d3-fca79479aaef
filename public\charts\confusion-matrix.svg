<svg width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc" />
      <stop offset="100%" style="stop-color:#e2e8f0" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="400" fill="url(#bgGradient)" stroke="#cbd5e1" stroke-width="1"/>
  
  <!-- Title -->
  <text x="300" y="30" text-anchor="middle" fill="#1e293b" font-family="Arial, sans-serif" font-size="18" font-weight="bold">Confusion Matrix</text>
  
  <!-- Matrix Grid -->
  <g transform="translate(150, 80)">
    <!-- Headers -->
    <text x="150" y="40" text-anchor="middle" fill="#475569" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Predicted</text>
    <text x="50" y="70" text-anchor="middle" fill="#475569" font-family="Arial, sans-serif" font-size="12">tumor</text>
    <text x="150" y="70" text-anchor="middle" fill="#475569" font-family="Arial, sans-serif" font-size="12">stroma</text>
    <text x="250" y="70" text-anchor="middle" fill="#475569" font-family="Arial, sans-serif" font-size="12">vessel</text>
    
    <!-- Y-axis label -->
    <text x="-20" y="150" text-anchor="middle" fill="#475569" font-family="Arial, sans-serif" font-size="14" font-weight="bold" transform="rotate(-90, -20, 150)">True</text>
    <text x="20" y="100" text-anchor="middle" fill="#475569" font-family="Arial, sans-serif" font-size="12">tumor</text>
    <text x="20" y="150" text-anchor="middle" fill="#475569" font-family="Arial, sans-serif" font-size="12">stroma</text>
    <text x="20" y="200" text-anchor="middle" fill="#475569" font-family="Arial, sans-serif" font-size="12">vessel</text>
    
    <!-- Matrix cells -->
    <!-- Row 1: tumor -->
    <rect x="25" y="80" width="50" height="40" fill="#1e40af" opacity="0.8"/>
    <text x="50" y="105" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">532</text>
    
    <rect x="125" y="80" width="50" height="40" fill="#e2e8f0"/>
    <text x="150" y="105" text-anchor="middle" fill="#475569" font-family="Arial, sans-serif" font-size="16">3</text>
    
    <rect x="225" y="80" width="50" height="40" fill="#e2e8f0"/>
    <text x="250" y="105" text-anchor="middle" fill="#475569" font-family="Arial, sans-serif" font-size="16">0</text>
    
    <!-- Row 2: stroma -->
    <rect x="25" y="130" width="50" height="40" fill="#e2e8f0"/>
    <text x="50" y="155" text-anchor="middle" fill="#475569" font-family="Arial, sans-serif" font-size="16">0</text>
    
    <rect x="125" y="130" width="50" height="40" fill="#059669" opacity="0.8"/>
    <text x="150" y="155" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">18</text>
    
    <rect x="225" y="130" width="50" height="40" fill="#e2e8f0"/>
    <text x="250" y="155" text-anchor="middle" fill="#475569" font-family="Arial, sans-serif" font-size="16">0</text>
    
    <!-- Row 3: vessel -->
    <rect x="25" y="180" width="50" height="40" fill="#dc2626" opacity="0.6"/>
    <text x="50" y="205" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">19</text>
    
    <rect x="125" y="180" width="50" height="40" fill="#e2e8f0"/>
    <text x="150" y="205" text-anchor="middle" fill="#475569" font-family="Arial, sans-serif" font-size="16">0</text>
    
    <rect x="225" y="180" width="50" height="40" fill="#e2e8f0"/>
    <text x="250" y="205" text-anchor="middle" fill="#475569" font-family="Arial, sans-serif" font-size="16">0</text>
  </g>
  
  <!-- Color Scale -->
  <g transform="translate(450, 100)">
    <text x="50" y="15" text-anchor="middle" fill="#475569" font-family="Arial, sans-serif" font-size="12">Count</text>
    <rect x="20" y="20" width="20" height="100" fill="url(#colorScale)"/>
    <text x="45" y="30" fill="#475569" font-family="Arial, sans-serif" font-size="10">500</text>
    <text x="45" y="125" fill="#475569" font-family="Arial, sans-serif" font-size="10">0</text>
  </g>
  
  <defs>
    <linearGradient id="colorScale" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af" />
      <stop offset="100%" style="stop-color:#e2e8f0" />
    </linearGradient>
  </defs>
  
  <!-- Placeholder text -->
  <text x="300" y="350" text-anchor="middle" fill="#64748b" font-family="Arial, sans-serif" font-size="12" style="opacity: 0.7">
    Placeholder - Replace with your actual confusion matrix image
  </text>
</svg>
