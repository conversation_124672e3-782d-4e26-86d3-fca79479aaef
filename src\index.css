@tailwind base;
@tailwind components;
@tailwind utilities;
 
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
 
    --primary: 206 100% 33%;
    --primary-foreground: 210 40% 98%;
 
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
 
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
 
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
 
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 206 100% 33%;
 
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-off-white text-primary-navy antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  h1 {
    @apply text-4xl font-bold text-primary-navy mb-4 leading-tight;
  }
  h2 {
    @apply text-3xl font-semibold text-primary-navy mb-3;
  }
  h3 {
    @apply text-2xl font-medium text-primary-navy mb-2;
  }
  p {
    @apply text-medium-gray leading-relaxed mb-4;
  }
}

@layer components {
  .medical-card {
    @apply bg-white rounded-lg shadow-lg p-6 transition-all duration-300 hover:shadow-xl;
  }
  
  .medical-button {
    @apply px-4 py-2 bg-cerulean text-white rounded-md hover:bg-opacity-90 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-cerulean focus:ring-opacity-50;
  }

  .section-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12;
  }

  .gradient-bg {
    @apply bg-gradient-to-br from-sky-blue via-medical-mint to-lavender;
  }

  .medical-input {
    @apply w-full px-4 py-2 border border-light-gray rounded-md focus:outline-none focus:ring-2 focus:ring-cerulean focus:border-transparent;
  }

  .subtle-shadow {
    @apply shadow-[0_4px_20px_-4px_rgba(0,0,0,0.1)];
  }
}

/* Transitions and Animations */
.fade-enter {
  opacity: 0;
}
.fade-enter-active {
  opacity: 1;
  transition: opacity 300ms ease-in;
}
.fade-exit {
  opacity: 1;
}
.fade-exit-active {
  opacity: 0;
  transition: opacity 300ms ease-out;
}

/* Background pattern */
.medical-pattern {
  background-color: #ffffff;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e6e6fa' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}