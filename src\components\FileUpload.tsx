import React, { useRef, useState } from 'react';
import { Card, CardContent } from './ui/card';
import { Button } from './ui/button';
import { FolderOpen } from 'lucide-react';
import { toast } from 'sonner';

interface FileUploadProps {
  onFilesSelected: (files: File[]) => void;
}

interface FileWithPath extends File {
  webkitRelativePath?: string;
}

export const FileUpload: React.FC<FileUploadProps> = ({ onFilesSelected }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [folderName, setFolderName] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const processEntries = async (entries: FileSystemEntry[], accumulatedFiles: File[] = []) => {
    const promises: Promise<void>[] = [];

    for (const entry of entries) {
      if (entry.isFile) {
        promises.push(
          new Promise<void>((resolve) => {
            (entry as FileSystemFileEntry).file((file) => {
              if (file.type.startsWith('image/')) {
                accumulatedFiles.push(file);
              }
              resolve();
            });
          })
        );
      } else if (entry.isDirectory) {
        promises.push(
          new Promise<void>((resolve) => {
            const dirReader = (entry as FileSystemDirectoryEntry).createReader();
            const readEntries = () => {
              dirReader.readEntries(async (subEntries) => {
                if (subEntries.length > 0) {
                  await processEntries(subEntries, accumulatedFiles);
                  readEntries(); // Continue reading if there are more entries
                } else {
                  resolve();
                }
              });
            };
            readEntries();
          })
        );
      }
    }

    await Promise.all(promises);
    return accumulatedFiles;
  };

  const handleDrop = async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(false);
    setUploading(true);

    try {
      const items = event.dataTransfer.items;
      const entries: FileSystemEntry[] = [];
      let folderFound = false;

      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        const entry = item.webkitGetAsEntry();
        if (entry) {
          entries.push(entry);
          if (entry.isDirectory) {
            folderFound = true;
            setFolderName(entry.name);
          }
        }
      }

      if (!folderFound) {
        toast.error("Please drop a folder, not individual files");
        setUploading(false);
        return;
      }

      toast.info(`Processing folder: ${entries[0].name}...`);
      const files = await processEntries(entries);

      if (files.length > 0) {
        onFilesSelected(files);
        toast.success(`${files.length} image files found in folder`);
      } else {
        toast.error("No image files found in the folder");
      }
    } catch (error) {
      console.error("Error processing dropped items:", error);
      toast.error("Error processing folder");
    }
    setUploading(false);
  };

  const handleFileInput = async (event: React.ChangeEvent<HTMLInputElement>) => {
    event.preventDefault();
    setUploading(true);
    
    try {
      const files = Array.from(event.target.files || []) as FileWithPath[];
      if (files.length === 0) {
        toast.error("No files selected");
        setUploading(false);
        return;
      }

      // Set the folder name from the common path prefix
      const folderPath = files[0].webkitRelativePath?.split('/')[0] || 'Selected Folder';
      setFolderName(folderPath);
      
      toast.info(`Processing folder: ${folderPath}...`);
      
      // Filter for image files
      const imageFiles = files.filter(file => file.type.startsWith('image/'));
      
      if (imageFiles.length > 0) {
        onFilesSelected(imageFiles);
        toast.success(`${imageFiles.length} image files found in folder`);
      } else {
        toast.error("No image files found in the folder");
      }
    } catch (error) {
      console.error("Error processing selected files:", error);
      toast.error("Error processing folder");
    }
    setUploading(false);
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(false);
  };

  const handleSelectFolder = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <Card className="w-full">
      <CardContent className="pt-6">
        <div
          className={`border-2 border-dashed rounded-lg p-10 text-center ${
            isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
          } transition-colors`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <FolderOpen className="h-10 w-10 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium mb-2">Upload Histopathology Folder</h3>
          <p className="text-sm text-gray-500 mb-4">
            Drag and drop a folder of patch images or click to browse
          </p>
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileInput}
            {...{webkitdirectory: "", directory: ""} as any}
            multiple
            className="hidden"
          />
          <Button
            onClick={handleSelectFolder}
            className="mt-2"
            disabled={uploading}
          >
            {uploading ? 'Processing...' : folderName ? `Change Folder (${folderName})` : 'Select Folder'}
          </Button>
          {folderName && !uploading && (
            <p className="mt-2 text-sm text-blue-500">Folder selected: {folderName}</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default FileUpload;
