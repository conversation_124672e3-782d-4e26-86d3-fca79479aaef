{"format": "graph-model", "generatedBy": "2.18.0", "convertedBy": "TensorFlow.js Converter v4.22.0", "signature": {"inputs": {"input_layer": {"name": "input_layer:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "1080"}, {"size": "566"}, {"size": "3"}]}}}, "outputs": {"output_1": {"name": "Identity_1:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "3"}]}}, "output_0": {"name": "Identity:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "4"}]}}}}, "modelTopology": {"node": [{"name": "StatefulPartitionedCall/functional_1/dense_1_2/Cast/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/dense_1_2/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/regression_1/Cast/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}, {"size": "4"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/regression_1/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_9_1/batchnorm/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "72"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_7_1/batchnorm/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "288"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_6_1/batchnorm/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_4_1/batchnorm/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_3_1/batchnorm/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_3_1/batchnorm/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_1_2/batchnorm/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_1_2/convolution/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_1_2/Squeeze", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_1_2/batchnorm/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_1/depthwise/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_1/Squeeze", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_2_1/batchnorm/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_2_1/batchnorm/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_2_1/convolution/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_2_1/Squeeze", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_1/batchnorm/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_1/convolution/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "3"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_1/Squeeze", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_1/batchnorm/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_3_1/convolution/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "192"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_3_1/Squeeze", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_4_1/batchnorm/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_1_2/depthwise/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "192"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_1_2/Squeeze", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_5_1/batchnorm/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_5_1/batchnorm/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_4_1/convolution/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "192"}, {"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_4_1/Squeeze", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_6_1/batchnorm/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_5_1/convolution/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "48"}, {"size": "288"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_5_1/Squeeze", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "288"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_7_1/batchnorm/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "288"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_2_1/depthwise/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "288"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_2_1/Squeeze", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "288"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_8_1/batchnorm/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "288"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_8_1/batchnorm/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "288"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_6_1/convolution/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "288"}, {"size": "72"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_6_1/Squeeze", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "72"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_9_1/batchnorm/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "72"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/global_average_pooling2d_1/Mean/reduction_indices", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/functional_1/dense_1/Cast/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "72"}, {"size": "512"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/dense_1/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/dense_2_1/Cast/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/dense_2_1/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/classification_1/Cast/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}, {"size": "3"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/classification_1/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "input_layer", "op": "Placeholder", "attr": {"shape": {"shape": {"dim": [{"size": "-1"}, {"size": "1080"}, {"size": "566"}, {"size": "3"}]}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_1/BiasAdd", "op": "_FusedConv2D", "input": ["input_layer", "StatefulPartitionedCall/functional_1/conv2d_1/convolution/ReadVariableOp", "StatefulPartitionedCall/functional_1/conv2d_1/Squeeze"], "device": "/device:CPU:0", "attr": {"num_host_args": {"i": "0"}, "data_format": {"s": "TkhXQw=="}, "filter_format": {"s": "SFdJTw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_1/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_1/batchnorm/mul", "StatefulPartitionedCall/functional_1/conv2d_1/BiasAdd"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_1/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/conv2d_1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_1/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/conv2d_1/mul_1", "StatefulPartitionedCall/functional_1/conv2d_1/Sigmoid"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_1/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_1/batchnorm/mul_1", "StatefulPartitionedCall/functional_1/batch_normalization_1/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_1_2/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_1/batchnorm/add_1", "StatefulPartitionedCall/functional_1/conv2d_1_2/convolution/ReadVariableOp", "StatefulPartitionedCall/functional_1/conv2d_1_2/Squeeze"], "device": "/device:CPU:0", "attr": {"num_host_args": {"i": "0"}, "data_format": {"s": "TkhXQw=="}, "filter_format": {"s": "SFdJTw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_1_2/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_1_2/batchnorm/mul", "StatefulPartitionedCall/functional_1/conv2d_1_2/BiasAdd"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_1_2/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/conv2d_1_2/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_1_2/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/conv2d_1_2/mul_1", "StatefulPartitionedCall/functional_1/conv2d_1_2/Sigmoid"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_1_2/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_1_2/batchnorm/mul_1", "StatefulPartitionedCall/functional_1/batch_normalization_1_2/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_1/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_1_2/batchnorm/add_1", "StatefulPartitionedCall/functional_1/depthwise_conv2d_1/depthwise/ReadVariableOp", "StatefulPartitionedCall/functional_1/depthwise_conv2d_1/Squeeze"], "attr": {"padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_2_1/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_1/depthwise", "StatefulPartitionedCall/functional_1/batch_normalization_2_1/batchnorm/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_2_1/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_2_1/batchnorm/mul_1", "StatefulPartitionedCall/functional_1/batch_normalization_2_1/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/activation_1/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_2_1/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/activation_1/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_2_1/batchnorm/add_1", "StatefulPartitionedCall/functional_1/activation_1/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_2_1/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/activation_1/mul_1", "StatefulPartitionedCall/functional_1/conv2d_2_1/convolution/ReadVariableOp", "StatefulPartitionedCall/functional_1/conv2d_2_1/Squeeze"], "device": "/device:CPU:0", "attr": {"num_host_args": {"i": "0"}, "data_format": {"s": "TkhXQw=="}, "filter_format": {"s": "SFdJTw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_2_1/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_3_1/batchnorm/mul", "StatefulPartitionedCall/functional_1/conv2d_2_1/BiasAdd"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_2_1/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/conv2d_2_1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_3_1/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/conv2d_2_1/mul_1", "StatefulPartitionedCall/functional_1/conv2d_2_1/Sigmoid"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/add_1/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_Add", "op": "AddN", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_3_1/batchnorm/mul_1", "StatefulPartitionedCall/functional_1/batch_normalization_1/batchnorm/add_1"], "attr": {"N": {"i": "2"}, "T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}}}, {"name": "StatefulPartitionedCall/functional_1/add_1/ArithmeticOptimizer/AddOpsRewrite_Add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_3_1/batchnorm/sub", "StatefulPartitionedCall/functional_1/add_1/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_Add"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_3_1/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/add_1/ArithmeticOptimizer/AddOpsRewrite_Add", "StatefulPartitionedCall/functional_1/conv2d_3_1/convolution/ReadVariableOp", "StatefulPartitionedCall/functional_1/conv2d_3_1/Squeeze"], "device": "/device:CPU:0", "attr": {"num_host_args": {"i": "0"}, "data_format": {"s": "TkhXQw=="}, "filter_format": {"s": "SFdJTw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_3_1/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_4_1/batchnorm/mul", "StatefulPartitionedCall/functional_1/conv2d_3_1/BiasAdd"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_3_1/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/conv2d_3_1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_4_1/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/conv2d_3_1/mul_1", "StatefulPartitionedCall/functional_1/conv2d_3_1/Sigmoid"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_4_1/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_4_1/batchnorm/mul_1", "StatefulPartitionedCall/functional_1/batch_normalization_4_1/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_1_2/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_4_1/batchnorm/add_1", "StatefulPartitionedCall/functional_1/depthwise_conv2d_1_2/depthwise/ReadVariableOp", "StatefulPartitionedCall/functional_1/depthwise_conv2d_1_2/Squeeze"], "attr": {"padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_5_1/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_1_2/depthwise", "StatefulPartitionedCall/functional_1/batch_normalization_5_1/batchnorm/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_5_1/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_5_1/batchnorm/mul_1", "StatefulPartitionedCall/functional_1/batch_normalization_5_1/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/activation_1_2/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_5_1/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/activation_1_2/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_5_1/batchnorm/add_1", "StatefulPartitionedCall/functional_1/activation_1_2/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_4_1/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/activation_1_2/mul_1", "StatefulPartitionedCall/functional_1/conv2d_4_1/convolution/ReadVariableOp", "StatefulPartitionedCall/functional_1/conv2d_4_1/Squeeze"], "device": "/device:CPU:0", "attr": {"num_host_args": {"i": "0"}, "data_format": {"s": "TkhXQw=="}, "filter_format": {"s": "SFdJTw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_4_1/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_6_1/batchnorm/mul", "StatefulPartitionedCall/functional_1/conv2d_4_1/BiasAdd"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_4_1/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/conv2d_4_1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_6_1/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/conv2d_4_1/mul_1", "StatefulPartitionedCall/functional_1/conv2d_4_1/Sigmoid"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_6_1/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_6_1/batchnorm/mul_1", "StatefulPartitionedCall/functional_1/batch_normalization_6_1/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_5_1/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_6_1/batchnorm/add_1", "StatefulPartitionedCall/functional_1/conv2d_5_1/convolution/ReadVariableOp", "StatefulPartitionedCall/functional_1/conv2d_5_1/Squeeze"], "device": "/device:CPU:0", "attr": {"num_host_args": {"i": "0"}, "data_format": {"s": "TkhXQw=="}, "filter_format": {"s": "SFdJTw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_5_1/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_7_1/batchnorm/mul", "StatefulPartitionedCall/functional_1/conv2d_5_1/BiasAdd"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_5_1/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/conv2d_5_1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_7_1/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/conv2d_5_1/mul_1", "StatefulPartitionedCall/functional_1/conv2d_5_1/Sigmoid"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_7_1/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_7_1/batchnorm/mul_1", "StatefulPartitionedCall/functional_1/batch_normalization_7_1/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_2_1/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_7_1/batchnorm/add_1", "StatefulPartitionedCall/functional_1/depthwise_conv2d_2_1/depthwise/ReadVariableOp", "StatefulPartitionedCall/functional_1/depthwise_conv2d_2_1/Squeeze"], "attr": {"padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_8_1/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_2_1/depthwise", "StatefulPartitionedCall/functional_1/batch_normalization_8_1/batchnorm/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_8_1/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_8_1/batchnorm/mul_1", "StatefulPartitionedCall/functional_1/batch_normalization_8_1/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/activation_2_1/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_8_1/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/activation_2_1/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_8_1/batchnorm/add_1", "StatefulPartitionedCall/functional_1/activation_2_1/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_6_1/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/activation_2_1/mul_1", "StatefulPartitionedCall/functional_1/conv2d_6_1/convolution/ReadVariableOp", "StatefulPartitionedCall/functional_1/conv2d_6_1/Squeeze"], "device": "/device:CPU:0", "attr": {"num_host_args": {"i": "0"}, "data_format": {"s": "TkhXQw=="}, "filter_format": {"s": "SFdJTw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_6_1/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_9_1/batchnorm/mul", "StatefulPartitionedCall/functional_1/conv2d_6_1/BiasAdd"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_6_1/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/conv2d_6_1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_9_1/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/conv2d_6_1/mul_1", "StatefulPartitionedCall/functional_1/conv2d_6_1/Sigmoid"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_9_1/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_9_1/batchnorm/mul_1", "StatefulPartitionedCall/functional_1/batch_normalization_9_1/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/global_average_pooling2d_1/Mean", "op": "Mean", "input": ["StatefulPartitionedCall/functional_1/batch_normalization_9_1/batchnorm/add_1", "StatefulPartitionedCall/functional_1/global_average_pooling2d_1/Mean/reduction_indices"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/functional_1/dense_1/Relu", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/functional_1/global_average_pooling2d_1/Mean", "StatefulPartitionedCall/functional_1/dense_1/Cast/ReadVariableOp", "StatefulPartitionedCall/functional_1/dense_1/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "transpose_b": {"b": false}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "leakyrelu_alpha": {"f": 0.2}, "num_args": {"i": "1"}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/dense_2_1/Relu", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/functional_1/dense_1/Relu", "StatefulPartitionedCall/functional_1/dense_2_1/Cast/ReadVariableOp", "StatefulPartitionedCall/functional_1/dense_2_1/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "transpose_b": {"b": false}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "num_args": {"i": "1"}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/dense_1_2/Relu", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/functional_1/dense_1/Relu", "StatefulPartitionedCall/functional_1/dense_1_2/Cast/ReadVariableOp", "StatefulPartitionedCall/functional_1/dense_1_2/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "transpose_b": {"b": false}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "leakyrelu_alpha": {"f": 0.2}, "num_args": {"i": "1"}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/classification_1/BiasAdd", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/functional_1/dense_2_1/Relu", "StatefulPartitionedCall/functional_1/classification_1/Cast/ReadVariableOp", "StatefulPartitionedCall/functional_1/classification_1/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "transpose_b": {"b": false}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "leakyrelu_alpha": {"f": 0.2}, "num_args": {"i": "1"}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/regression_1/BiasAdd", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/functional_1/dense_1_2/Relu", "StatefulPartitionedCall/functional_1/regression_1/Cast/ReadVariableOp", "StatefulPartitionedCall/functional_1/regression_1/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "transpose_b": {"b": false}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/classification_1/Softmax", "op": "Softmax", "input": ["StatefulPartitionedCall/functional_1/classification_1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity", "op": "Identity", "input": ["StatefulPartitionedCall/functional_1/regression_1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity_1", "op": "Identity", "input": ["StatefulPartitionedCall/functional_1/classification_1/Softmax"], "attr": {"T": {"type": "DT_FLOAT"}}}], "library": {}, "versions": {"producer": 1994}}, "weightsManifest": [{"paths": ["group1-shard1of1.bin"], "weights": [{"name": "StatefulPartitionedCall/functional_1/dense_1_2/Cast/ReadVariableOp", "shape": [512, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/dense_1_2/BiasAdd/ReadVariableOp", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/regression_1/Cast/ReadVariableOp", "shape": [128, 4], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/regression_1/BiasAdd/ReadVariableOp", "shape": [4], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_9_1/batchnorm/mul", "shape": [72], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_7_1/batchnorm/mul", "shape": [288], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_6_1/batchnorm/mul", "shape": [48], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_4_1/batchnorm/mul", "shape": [192], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_3_1/batchnorm/sub", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_3_1/batchnorm/mul", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_1_2/batchnorm/mul", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/conv2d_1_2/convolution/ReadVariableOp", "shape": [1, 1, 32, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/conv2d_1_2/Squeeze", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_1_2/batchnorm/sub", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_1/depthwise/ReadVariableOp", "shape": [3, 3, 128, 1], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_1/Squeeze", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_2_1/batchnorm/mul", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_2_1/batchnorm/sub", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/conv2d_2_1/convolution/ReadVariableOp", "shape": [1, 1, 128, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/conv2d_2_1/Squeeze", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_1/batchnorm/mul", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/conv2d_1/convolution/ReadVariableOp", "shape": [3, 3, 3, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/conv2d_1/Squeeze", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_1/batchnorm/sub", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/conv2d_3_1/convolution/ReadVariableOp", "shape": [1, 1, 32, 192], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/conv2d_3_1/Squeeze", "shape": [192], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_4_1/batchnorm/sub", "shape": [192], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_1_2/depthwise/ReadVariableOp", "shape": [3, 3, 192, 1], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_1_2/Squeeze", "shape": [192], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_5_1/batchnorm/mul", "shape": [192], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_5_1/batchnorm/sub", "shape": [192], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/conv2d_4_1/convolution/ReadVariableOp", "shape": [1, 1, 192, 48], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/conv2d_4_1/Squeeze", "shape": [48], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_6_1/batchnorm/sub", "shape": [48], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/conv2d_5_1/convolution/ReadVariableOp", "shape": [1, 1, 48, 288], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/conv2d_5_1/Squeeze", "shape": [288], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_7_1/batchnorm/sub", "shape": [288], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_2_1/depthwise/ReadVariableOp", "shape": [3, 3, 288, 1], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_2_1/Squeeze", "shape": [288], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_8_1/batchnorm/mul", "shape": [288], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_8_1/batchnorm/sub", "shape": [288], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/conv2d_6_1/convolution/ReadVariableOp", "shape": [1, 1, 288, 72], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/conv2d_6_1/Squeeze", "shape": [72], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/batch_normalization_9_1/batchnorm/sub", "shape": [72], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/global_average_pooling2d_1/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/functional_1/dense_1/Cast/ReadVariableOp", "shape": [72, 512], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/dense_1/BiasAdd/ReadVariableOp", "shape": [512], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/dense_2_1/Cast/ReadVariableOp", "shape": [512, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/dense_2_1/BiasAdd/ReadVariableOp", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/classification_1/Cast/ReadVariableOp", "shape": [128, 3], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/classification_1/BiasAdd/ReadVariableOp", "shape": [3], "dtype": "float32"}]}]}