import React from 'react';
import { MedicalCard } from './ui/medical-card';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

const endometrialCancerInfo = [
  {
    title: "What is Endometrial Cancer?",
    content: `Endometrial cancer is a type of cancer that begins in the lining of the uterus (endometrium). 
    It is one of the most common gynecologic cancers and typically develops after menopause. Early detection 
    through innovative technologies like our patch analyzer can significantly improve treatment outcomes.`
  },
  {
    title: "Common Symptoms",
    content: `• Abnormal vaginal bleeding or discharge
    • Pelvic pain or pressure
    • Unexplained weight loss
    • Difficulty urinating
    • Pain during intercourse
    Early recognition of these symptoms and prompt medical attention are crucial for better outcomes.`
  },
  {
    title: "Risk Factors",
    content: `Several factors may increase the risk of endometrial cancer:
    • Age (most cases occur after age 50)
    • Obesity
    • Hormone imbalances
    • Diabetes
    • High blood pressure
    • Family history of certain cancers
    Understanding these risk factors helps in prevention and early detection.`
  },
  {
    title: "Diagnosis & Treatment",
    content: `Modern diagnosis methods include:
    • Physical examination
    • Innovative patch analysis technology
    • Ultrasound
    • Biopsy
    
    Treatment options vary based on stage and may include:
    • Surgery
    • Radiation therapy
    • Chemotherapy
    • Hormone therapy`
  },
  {
    title: "Prevention & Early Detection",
    content: `Prevention strategies include:
    • Maintaining a healthy weight
    • Regular exercise
    • Balanced diet
    • Regular medical check-ups
    
    Our patch analyzer technology provides a non-invasive method for early detection, 
    improving the chances of successful treatment.`
  }
];

export const AboutSection = () => {
  return (
    <section className="py-16 bg-off-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold text-primary-navy text-center mb-8">
            Understanding Endometrial Cancer
          </h2>
          
          <p className="text-medium-gray text-center mb-12">
            Knowledge is power in cancer prevention and treatment. Learn about endometrial cancer, 
            its symptoms, risk factors, and how our innovative patch analyzer aids in early detection.
          </p>

          <MedicalCard className="mb-8">
            <Accordion type="single" collapsible className="w-full">
              {endometrialCancerInfo.map((info, index) => (
                <AccordionItem key={index} value={`item-${index}`}>
                  <AccordionTrigger className="text-lg font-semibold text-primary-navy hover:text-cerulean">
                    {info.title}
                  </AccordionTrigger>
                  <AccordionContent className="text-medium-gray whitespace-pre-line">
                    {info.content}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </MedicalCard>

          <div className="text-center">
            <p className="text-sm text-medium-gray">
              Always consult with healthcare professionals for medical advice. 
              Our patch analyzer is a screening tool to aid in early detection.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};
