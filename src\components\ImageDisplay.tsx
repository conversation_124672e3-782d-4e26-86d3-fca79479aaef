import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ZoomInIcon, 
  ZoomOutIcon, 
  RotateCcwIcon, 
  ImageIcon,
  MaximizeIcon,
  Grid2X2Icon
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface ImageDisplayProps {
  file: File;
  label?: string;
  percentage?: number;
  patchNumber?: number;
  predictions?: { label: string; probability: number }[];
  onImageClick?: (index: number) => void;
}

const ImageDisplay: React.FC<ImageDisplayProps> = ({ file, label, percentage, patchNumber, predictions, onImageClick }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [viewMode, setViewMode] = useState<'grid' | 'single'>('grid');

  useEffect(() => {
    if (!canvasRef.current || !file) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Load the image
    const img = new Image();
    img.onload = () => {
      // Set canvas dimensions to match the image
      canvas.width = img.width;
      canvas.height = img.height;

      // Draw the original image
      ctx.drawImage(img, 0, 0);

      // Add label if provided
      if (label) {
        // Add a semi-transparent background for the label
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(0, 0, canvas.width, 30);

        // Add the label text
        ctx.fillStyle = 'white';
        ctx.font = 'bold 14px Arial';
        ctx.textAlign = 'center';

        // Create label with patch number if available
        let labelText = `${label}${percentage !== undefined ? `: ${percentage}%` : ''}`;
        if (patchNumber !== undefined) {
          labelText += ` (Patch #${patchNumber})`;
        }

        ctx.fillText(labelText, canvas.width / 2, 20);
      }
    };

    img.onerror = (error) => {
      console.error('Error loading image:', error);

      // Draw an error message
      ctx.fillStyle = '#f0f0f0';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      ctx.fillStyle = '#ff0000';
      ctx.font = '16px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('Error loading image', canvas.width / 2, canvas.height / 2);
    };

    // Load the image
    img.src = URL.createObjectURL(file);

    // Clean up
    return () => {
      URL.revokeObjectURL(img.src);
    };
  }, [file, label, percentage, patchNumber]);

  const handleZoomIn = () => setZoom(prev => Math.min(prev + 0.25, 3));
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 0.25, 0.5));
  const handleRotate = () => setRotation(prev => (prev + 90) % 360);
  const handleReset = () => {
    setZoom(1);
    setRotation(0);
  };

  const toggleViewMode = () => {
    setViewMode(prev => prev === 'grid' ? 'single' : 'grid');
  };

  const handleImageSelect = (index: number) => {
    setSelectedImageIndex(index);
    onImageClick?.(index);
  };

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Tissue Analysis Results
        </h3>
        <div className="flex gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={toggleViewMode}
                  className="text-sky-700 hover:text-sky-800 border-sky-200 hover:border-sky-300"
                >
                  {viewMode === 'grid' ? <MaximizeIcon className="h-4 w-4" /> : <Grid2X2Icon className="h-4 w-4" />}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Toggle view mode</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          {selectedImageIndex !== null && (
            <>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={handleZoomIn}
                      className="text-sky-700 hover:text-sky-800 border-sky-200 hover:border-sky-300"
                    >
                      <ZoomInIcon className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Zoom in</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={handleZoomOut}
                      className="text-sky-700 hover:text-sky-800 border-sky-200 hover:border-sky-300"
                    >
                      <ZoomOutIcon className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Zoom out</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={handleRotate}
                      className="text-sky-700 hover:text-sky-800 border-sky-200 hover:border-sky-300"
                    >
                      <RotateCcwIcon className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Rotate image</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={handleReset}
                      className="text-sky-700 hover:text-sky-800 border-sky-200 hover:border-sky-300"
                    >
                      <ImageIcon className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Reset view</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </>
          )}
        </div>
      </div>

      <div className={`grid ${viewMode === 'grid' ? 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4' : 'grid-cols-1'} gap-4`}>
        <AnimatePresence>
          {Array.isArray(file) && file.map((image, index) => (
            <motion.div
              key={image.name}
              layout
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.2 }}
              className={`relative ${selectedImageIndex === index ? 'col-span-full row-span-full' : ''}`}
            >
              <Card
                className={`
                  relative overflow-hidden cursor-pointer transition-shadow duration-200
                  hover:shadow-lg border-sky-100 hover:border-sky-200
                  ${selectedImageIndex === index ? 'aspect-video' : 'aspect-square'}
                `}
                onClick={() => handleImageSelect(index)}
              >
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-200" />
                <motion.canvas
                  ref={canvasRef}
                  className="w-full h-full object-cover"
                  width={300}
                  height={200}
                  style={{
                    scale: selectedImageIndex === index ? zoom : 1,
                    rotate: selectedImageIndex === index ? rotation : 0,
                  }}
                  transition={{ duration: 0.3 }}
                />
                {predictions && predictions[index] && (
                  <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/60 to-transparent text-white">
                    <p className="text-sm font-medium">
                      {predictions[index].label}:{' '}
                      <span className="font-bold">
                        {(predictions[index].probability * 100).toFixed(1)}%
                      </span>
                    </p>
                  </div>
                )}
              </Card>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default ImageDisplay;
