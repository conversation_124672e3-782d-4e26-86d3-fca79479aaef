<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="microscope-cells" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
      <!-- Cell structures -->
      <circle cx="30" cy="30" r="20" fill="none" stroke="#E0F2FE" stroke-width="1" opacity="0.3"/>
      <circle cx="30" cy="30" r="12" fill="none" stroke="#7DD3FC" stroke-width="0.8" opacity="0.2"/>
      <circle cx="30" cy="30" r="6" fill="#0EA5E9" opacity="0.1"/>
      <circle cx="25" cy="25" r="2" fill="#0284C7" opacity="0.15"/>
      <circle cx="35" cy="35" r="1.5" fill="#0284C7" opacity="0.15"/>
      
      <!-- Molecular structures -->
      <path d="M15 15L20 20M40 40L45 45M15 45L20 40M40 15L45 20" stroke="#38BDF8" stroke-width="0.5" opacity="0.2"/>
    </pattern>
    
    <pattern id="medical-cross" x="0" y="0" width="80" height="80" patternUnits="userSpaceOnUse">
      <path d="M35 20v20M25 30h20" stroke="#E0F2FE" stroke-width="2" opacity="0.1"/>
      <circle cx="40" cy="30" r="15" fill="none" stroke="#F0F9FF" stroke-width="1" opacity="0.1"/>
    </pattern>
  </defs>
  
  <rect width="200" height="200" fill="url(#microscope-cells)"/>
  <rect width="200" height="200" fill="url(#medical-cross)"/>
</svg>
